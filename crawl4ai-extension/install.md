# 🚀 Crawl4AI 浏览器扩展安装指南

## 📋 安装前准备

### 系统要求
- Chrome 88+ 或 Edge 88+
- 支持Manifest V3的现代浏览器

### 可选依赖
- **Crawl4AI Docker服务器** (用于高级功能)
  ```bash
  docker run -d -p 11235:11235 --name crawl4ai unclecode/crawl4ai:0.6.0-r1
  ```

## 🔧 安装步骤

### 方法1: 开发者模式安装 (推荐)

1. **下载扩展文件**
   - 下载并解压 `crawl4ai-extension` 文件夹到本地

2. **准备图标文件** (重要!)
   - 在 `icons/` 文件夹中添加以下图标文件：
     - `icon16.png` (16x16)
     - `icon32.png` (32x32) 
     - `icon48.png` (48x48)
     - `icon128.png` (128x128)
   
   **快速解决方案**: 如果没有图标，可以创建简单的彩色方块作为临时图标

3. **打开Chrome扩展管理**
   - 在地址栏输入: `chrome://extensions/`
   - 或菜单 → 更多工具 → 扩展程序

4. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

5. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `crawl4ai-extension` 文件夹
   - 点击"选择文件夹"

6. **验证安装**
   - 扩展应该出现在扩展列表中
   - 浏览器工具栏会显示扩展图标

### 方法2: 打包安装

1. **打包扩展**
   - 在扩展管理页面点击"打包扩展程序"
   - 选择扩展文件夹
   - 生成 `.crx` 和 `.pem` 文件

2. **安装打包文件**
   - 将 `.crx` 文件拖拽到扩展管理页面
   - 点击"添加扩展程序"确认安装

## ⚙️ 初始配置

### 基础设置

1. **点击扩展图标**
2. **切换到"配置"标签页**
3. **配置基本选项**:
   - ✅ 自动清理内容
   - ✅ 移除广告内容
   - 最小字数: 10
   - 排除标签: nav,footer,aside

### API配置 (可选)

如果您有Crawl4AI服务器或LLM API:

1. **切换到"配置"标签页**
2. **设置API端点**:
   - Crawl4AI服务器: `http://localhost:11235`
3. **配置LLM API密钥** (如需要):
   - OpenAI: `sk-...`
   - Claude: `sk-ant-...`
   - 等等

4. **点击"保存配置"**

## 🎯 快速测试

### 测试基础功能

1. **访问任意网页** (如: https://example.com)
2. **点击扩展图标**
3. **选择"Markdown"模式**
4. **点击"开始提取"**
5. **查看提取结果**

### 测试CSS选择器

1. **访问新闻网站**
2. **选择"CSS选择器"模式**
3. **输入选择器**: `article` 或 `.content`
4. **点击"开始提取"**

### 测试元素选择

1. **点击扩展图标**
2. **点击"提取选中内容"**
3. **在页面上选择文本**
4. **再次点击按钮查看结果**

## 🔧 故障排除

### 常见问题

#### 1. 扩展无法加载
**症状**: 加载时出现错误
**解决方案**:
- 检查 `manifest.json` 语法
- 确保所有引用的文件存在
- 查看控制台错误信息

#### 2. 图标不显示
**症状**: 扩展列表中显示默认图标
**解决方案**:
- 确保 `icons/` 文件夹包含所需图标
- 检查图标文件名和路径
- 重新加载扩展

#### 3. 功能不工作
**症状**: 点击按钮无反应
**解决方案**:
- 按F12打开开发者工具查看错误
- 检查页面是否完全加载
- 尝试刷新页面后重试

#### 4. 权限问题
**症状**: 无法访问页面内容
**解决方案**:
- 确认扩展有必要权限
- 检查 `manifest.json` 中的权限配置
- 重新安装扩展

### 调试方法

1. **启用调试模式**:
   - 右键扩展图标 → 选项
   - 高级设置 → 启用调试模式

2. **查看扩展日志**:
   - 扩展管理页面 → 点击"错误"
   - 或右键扩展图标 → 检查弹出窗口

3. **检查内容脚本**:
   - 在网页上按F12
   - Console标签页查看错误信息

## 📱 使用技巧

### 快捷键
- `Ctrl + Shift + E`: 快速打开提取菜单
- `ESC`: 退出元素选择模式

### 右键菜单
- 右键页面 → "提取整个页面"
- 右键选中文本 → "提取选中内容"
- 右键链接 → "提取链接内容"

### 浮动按钮
- 页面右上角的🚀按钮
- 点击显示快速操作菜单

## 🔄 更新扩展

### 开发版本更新
1. 下载新版本文件
2. 覆盖原文件夹
3. 在扩展管理页面点击"重新加载"

### 保留设置
- 扩展设置会自动保存
- 历史记录会保留
- API密钥需要重新配置

## 🆘 获取帮助

如果遇到问题:

1. **查看README文档**
2. **检查GitHub Issues**
3. **提交新Issue**
4. **访问Crawl4AI官方文档**

## 🎉 安装完成

恭喜！您已成功安装Crawl4AI浏览器扩展。

现在您可以：
- 🚀 快速提取网页内容
- 🎯 精确选择页面元素
- 🤖 使用AI智能分析
- 📊 导出结构化数据

**开始您的智能网页内容提取之旅吧！**
