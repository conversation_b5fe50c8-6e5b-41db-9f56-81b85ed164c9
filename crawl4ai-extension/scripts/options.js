// 选项页面脚本
class Crawl4AIOptions {
    constructor() {
        this.currentTab = 'general';
        this.settings = {};
        this.init();
    }

    async init() {
        await this.loadSettings();
        this.setupEventListeners();
        this.updateUI();
    }

    // 加载设置
    async loadSettings() {
        const syncSettings = await chrome.storage.sync.get();
        const localSettings = await chrome.storage.local.get(['debugMode', 'cacheSize', 'cacheTtl']);
        
        this.settings = {
            // 通用设置
            autoClean: syncSettings.autoClean ?? true,
            removeAds: syncSettings.removeAds ?? true,
            extractLinks: syncSettings.extractLinks ?? false,
            extractImages: syncSettings.extractImages ?? false,
            showFloatingButton: syncSettings.showFloatingButton ?? true,
            showNotifications: syncSettings.showNotifications ?? true,
            
            // 提取设置
            minWords: syncSettings.minWords ?? 10,
            excludeTags: syncSettings.excludeTags ?? 'nav,footer,aside',
            excludeSelectors: syncSettings.excludeSelectors ?? '.ad,.popup,.banner',
            defaultMode: syncSettings.defaultMode ?? 'markdown',
            smartContentDetection: syncSettings.smartContentDetection ?? true,
            preserveFormatting: syncSettings.preserveFormatting ?? false,
            
            // API设置
            apiEndpoint: syncSettings.apiEndpoint ?? 'http://localhost:11235',
            apiTimeout: syncSettings.apiTimeout ?? 30,
            openaiKey: syncSettings.openaiKey ?? '',
            claudeKey: syncSettings.claudeKey ?? '',
            geminiKey: syncSettings.geminiKey ?? '',
            localLlmEndpoint: syncSettings.localLlmEndpoint ?? 'http://localhost:11434',
            firecrawlKey: syncSettings.firecrawlKey ?? '',
            
            // 高级设置
            concurrentRequests: syncSettings.concurrentRequests ?? 3,
            cacheSize: localSettings.cacheSize ?? 100,
            cacheTtl: localSettings.cacheTtl ?? 24,
            debugMode: localSettings.debugMode ?? false,
            saveRawHtml: syncSettings.saveRawHtml ?? false
        };
    }

    // 更新UI
    updateUI() {
        // 通用设置
        document.getElementById('auto-clean').checked = this.settings.autoClean;
        document.getElementById('remove-ads').checked = this.settings.removeAds;
        document.getElementById('extract-links').checked = this.settings.extractLinks;
        document.getElementById('extract-images').checked = this.settings.extractImages;
        document.getElementById('show-floating-button').checked = this.settings.showFloatingButton;
        document.getElementById('show-notifications').checked = this.settings.showNotifications;
        
        // 提取设置
        document.getElementById('min-words').value = this.settings.minWords;
        document.getElementById('exclude-tags').value = this.settings.excludeTags;
        document.getElementById('exclude-selectors').value = this.settings.excludeSelectors;
        document.querySelector(`input[name="default-mode"][value="${this.settings.defaultMode}"]`).checked = true;
        document.getElementById('smart-content-detection').checked = this.settings.smartContentDetection;
        document.getElementById('preserve-formatting').checked = this.settings.preserveFormatting;
        
        // API设置
        document.getElementById('api-endpoint').value = this.settings.apiEndpoint;
        document.getElementById('api-timeout').value = this.settings.apiTimeout;
        document.getElementById('openai-key').value = this.settings.openaiKey;
        document.getElementById('claude-key').value = this.settings.claudeKey;
        document.getElementById('gemini-key').value = this.settings.geminiKey;
        document.getElementById('local-llm-endpoint').value = this.settings.localLlmEndpoint;
        document.getElementById('firecrawl-key').value = this.settings.firecrawlKey;
        
        // 高级设置
        document.getElementById('concurrent-requests').value = this.settings.concurrentRequests;
        document.getElementById('cache-size').value = this.settings.cacheSize;
        document.getElementById('cache-ttl').value = this.settings.cacheTtl;
        document.getElementById('debug-mode').checked = this.settings.debugMode;
        document.getElementById('save-raw-html').checked = this.settings.saveRawHtml;
    }

    // 设置事件监听器
    setupEventListeners() {
        // 标签页切换
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 保存设置
        document.getElementById('save-all').addEventListener('click', () => {
            this.saveAllSettings();
        });

        // 测试连接
        document.getElementById('test-connection').addEventListener('click', () => {
            this.testConnection();
        });

        // 数据管理
        document.getElementById('export-settings').addEventListener('click', () => {
            this.exportSettings();
        });

        document.getElementById('import-settings').addEventListener('click', () => {
            this.importSettings();
        });

        document.getElementById('reset-settings').addEventListener('click', () => {
            this.resetSettings();
        });

        document.getElementById('clear-cache').addEventListener('click', () => {
            this.clearCache();
        });

        document.getElementById('clear-history').addEventListener('click', () => {
            this.clearHistory();
        });

        // 实时保存某些设置
        document.querySelectorAll('input, select').forEach(element => {
            element.addEventListener('change', () => {
                this.autoSave();
            });
        });
    }

    // 切换标签页
    switchTab(tabName) {
        // 隐藏所有标签页
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // 显示选中的标签页
        document.getElementById(`${tabName}-tab`).classList.add('active');
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        this.currentTab = tabName;
    }

    // 收集当前设置
    collectSettings() {
        return {
            // 通用设置
            autoClean: document.getElementById('auto-clean').checked,
            removeAds: document.getElementById('remove-ads').checked,
            extractLinks: document.getElementById('extract-links').checked,
            extractImages: document.getElementById('extract-images').checked,
            showFloatingButton: document.getElementById('show-floating-button').checked,
            showNotifications: document.getElementById('show-notifications').checked,
            
            // 提取设置
            minWords: parseInt(document.getElementById('min-words').value),
            excludeTags: document.getElementById('exclude-tags').value,
            excludeSelectors: document.getElementById('exclude-selectors').value,
            defaultMode: document.querySelector('input[name="default-mode"]:checked').value,
            smartContentDetection: document.getElementById('smart-content-detection').checked,
            preserveFormatting: document.getElementById('preserve-formatting').checked,
            
            // API设置
            apiEndpoint: document.getElementById('api-endpoint').value,
            apiTimeout: parseInt(document.getElementById('api-timeout').value),
            openaiKey: document.getElementById('openai-key').value,
            claudeKey: document.getElementById('claude-key').value,
            geminiKey: document.getElementById('gemini-key').value,
            localLlmEndpoint: document.getElementById('local-llm-endpoint').value,
            firecrawlKey: document.getElementById('firecrawl-key').value,
            
            // 高级设置
            concurrentRequests: parseInt(document.getElementById('concurrent-requests').value),
            cacheSize: parseInt(document.getElementById('cache-size').value),
            cacheTtl: parseInt(document.getElementById('cache-ttl').value),
            debugMode: document.getElementById('debug-mode').checked,
            saveRawHtml: document.getElementById('save-raw-html').checked
        };
    }

    // 保存所有设置
    async saveAllSettings() {
        const settings = this.collectSettings();
        
        try {
            // 分别保存到sync和local存储
            const syncSettings = { ...settings };
            delete syncSettings.debugMode;
            delete syncSettings.cacheSize;
            delete syncSettings.cacheTtl;
            
            const localSettings = {
                debugMode: settings.debugMode,
                cacheSize: settings.cacheSize,
                cacheTtl: settings.cacheTtl
            };
            
            await chrome.storage.sync.set(syncSettings);
            await chrome.storage.local.set(localSettings);
            
            this.settings = settings;
            this.showSaveStatus('设置已保存', 'success');
            
        } catch (error) {
            console.error('保存设置失败:', error);
            this.showSaveStatus('保存失败: ' + error.message, 'error');
        }
    }

    // 自动保存
    async autoSave() {
        // 延迟保存，避免频繁操作
        clearTimeout(this.autoSaveTimer);
        this.autoSaveTimer = setTimeout(() => {
            this.saveAllSettings();
        }, 1000);
    }

    // 测试连接
    async testConnection() {
        const endpoint = document.getElementById('api-endpoint').value;
        const statusDiv = document.getElementById('connection-status');
        
        statusDiv.textContent = '正在测试连接...';
        statusDiv.className = 'status-indicator';
        statusDiv.style.display = 'block';
        
        try {
            const response = await fetch(`${endpoint}/health`, {
                method: 'GET',
                timeout: 5000
            });
            
            if (response.ok) {
                statusDiv.textContent = '连接成功';
                statusDiv.className = 'status-indicator success';
            } else {
                statusDiv.textContent = `连接失败: ${response.status}`;
                statusDiv.className = 'status-indicator error';
            }
        } catch (error) {
            statusDiv.textContent = `连接失败: ${error.message}`;
            statusDiv.className = 'status-indicator error';
        }
    }

    // 导出设置
    exportSettings() {
        const settings = this.collectSettings();
        const dataStr = JSON.stringify(settings, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `crawl4ai-settings-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(link.href);
    }

    // 导入设置
    importSettings() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const settings = JSON.parse(e.target.result);
                    this.settings = { ...this.settings, ...settings };
                    this.updateUI();
                    this.saveAllSettings();
                    this.showSaveStatus('设置已导入', 'success');
                } catch (error) {
                    this.showSaveStatus('导入失败: 文件格式错误', 'error');
                }
            };
            reader.readAsText(file);
        };
        
        input.click();
    }

    // 重置设置
    async resetSettings() {
        if (!confirm('确定要重置所有设置吗？此操作不可撤销。')) {
            return;
        }
        
        try {
            await chrome.storage.sync.clear();
            await chrome.storage.local.clear();
            
            // 重新加载默认设置
            await this.loadSettings();
            this.updateUI();
            
            this.showSaveStatus('设置已重置', 'success');
        } catch (error) {
            this.showSaveStatus('重置失败: ' + error.message, 'error');
        }
    }

    // 清空缓存
    async clearCache() {
        try {
            await chrome.storage.local.remove(['cache']);
            this.showSaveStatus('缓存已清空', 'success');
        } catch (error) {
            this.showSaveStatus('清空缓存失败: ' + error.message, 'error');
        }
    }

    // 清空历史
    async clearHistory() {
        if (!confirm('确定要清空所有历史记录吗？')) {
            return;
        }
        
        try {
            await chrome.storage.local.remove(['history']);
            this.showSaveStatus('历史记录已清空', 'success');
        } catch (error) {
            this.showSaveStatus('清空历史失败: ' + error.message, 'error');
        }
    }

    // 显示保存状态
    showSaveStatus(message, type) {
        const statusDiv = document.getElementById('save-status');
        statusDiv.textContent = message;
        statusDiv.className = `save-status ${type}`;
        
        // 3秒后清除状态
        setTimeout(() => {
            statusDiv.textContent = '';
            statusDiv.className = 'save-status';
        }, 3000);
    }
}

// 初始化选项页面
document.addEventListener('DOMContentLoaded', () => {
    new Crawl4AIOptions();
});
