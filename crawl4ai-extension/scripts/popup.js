// 弹窗主要逻辑
class Crawl4AIPopup {
    constructor() {
        this.currentTab = 'extract';
        this.config = {};
        this.init();
    }

    async init() {
        await this.loadConfig();
        this.setupEventListeners();
        this.setupTabs();
        this.loadHistory();
    }

    // 加载配置
    async loadConfig() {
        const result = await chrome.storage.sync.get([
            'autoClean', 'removeAds', 'extractLinks', 'extractImages',
            'minWords', 'excludeTags', 'apiEndpoint', 'firecrawlKey'
        ]);
        
        this.config = {
            autoClean: result.autoClean || true,
            removeAds: result.removeAds || true,
            extractLinks: result.extractLinks || false,
            extractImages: result.extractImages || false,
            minWords: result.minWords || 10,
            excludeTags: result.excludeTags || 'nav,footer,aside',
            apiEndpoint: result.apiEndpoint || 'http://localhost:11235',
            firecrawlKey: result.firecrawlKey || ''
        };

        this.updateConfigUI();
    }

    // 更新配置界面
    updateConfigUI() {
        document.getElementById('auto-clean').checked = this.config.autoClean;
        document.getElementById('remove-ads').checked = this.config.removeAds;
        document.getElementById('extract-links').checked = this.config.extractLinks;
        document.getElementById('extract-images').checked = this.config.extractImages;
        document.getElementById('min-words').value = this.config.minWords;
        document.getElementById('exclude-tags').value = this.config.excludeTags;
        document.getElementById('api-endpoint').value = this.config.apiEndpoint;
        document.getElementById('firecrawl-key').value = this.config.firecrawlKey;
    }

    // 设置事件监听器
    setupEventListeners() {
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 提取模式切换
        document.getElementById('extraction-mode').addEventListener('change', (e) => {
            this.showConfigSection(e.target.value);
        });

        // 提取按钮
        document.getElementById('extract-btn').addEventListener('click', () => {
            this.extractContent();
        });

        // 提取选中内容
        document.getElementById('extract-selection-btn').addEventListener('click', () => {
            this.extractSelection();
        });

        // 结果操作
        document.getElementById('copy-result')?.addEventListener('click', () => {
            this.copyResult();
        });

        document.getElementById('download-result')?.addEventListener('click', () => {
            this.downloadResult();
        });

        document.getElementById('save-result')?.addEventListener('click', () => {
            this.saveResult();
        });

        // 保存配置
        document.getElementById('save-config').addEventListener('click', () => {
            this.saveConfig();
        });

        // 清空历史
        document.getElementById('clear-history').addEventListener('click', () => {
            this.clearHistory();
        });
    }

    // 标签页切换
    switchTab(tabName) {
        // 隐藏所有标签页
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // 显示选中的标签页
        document.getElementById(`${tabName}-tab`).classList.add('active');
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        this.currentTab = tabName;
    }

    // 显示配置区域
    showConfigSection(mode) {
        // 隐藏所有配置区域
        document.querySelectorAll('.config-section').forEach(section => {
            section.style.display = 'none';
        });

        // 显示对应的配置区域
        const configSection = document.getElementById(`${mode}-config`);
        if (configSection) {
            configSection.style.display = 'block';
        }
    }

    // 获取当前标签页信息
    async getCurrentTab() {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        return tab;
    }

    // 主要提取功能
    async extractContent() {
        const tab = await this.getCurrentTab();
        const mode = document.getElementById('extraction-mode').value;
        
        this.showLoading(true);
        
        try {
            let result;
            
            switch (mode) {
                case 'markdown':
                    result = await this.extractMarkdown(tab);
                    break;
                case 'css':
                    result = await this.extractWithCSS(tab);
                    break;
                case 'regex':
                    result = await this.extractWithRegex(tab);
                    break;
                case 'llm':
                    result = await this.extractWithLLM(tab);
                    break;
                default:
                    throw new Error('未知的提取模式');
            }

            this.showResult(result);
            this.saveToHistory(tab.url, mode, result);
            
        } catch (error) {
            this.showError(error.message);
        } finally {
            this.showLoading(false);
        }
    }

    // Markdown提取
    async extractMarkdown(tab) {
        const [result] = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            function: extractMarkdownContent,
            args: [this.config]
        });
        return result.result;
    }

    // CSS选择器提取
    async extractWithCSS(tab) {
        const selector = document.getElementById('css-selector').value;
        const fieldsText = document.getElementById('css-fields').value;
        
        if (!selector) {
            throw new Error('请输入CSS选择器');
        }

        let fields = [];
        if (fieldsText) {
            try {
                fields = JSON.parse(fieldsText);
            } catch (e) {
                throw new Error('字段配置格式错误');
            }
        }

        const [result] = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            function: extractWithCSSSelector,
            args: [selector, fields, this.config]
        });
        return result.result;
    }

    // 正则表达式提取
    async extractWithRegex(tab) {
        const patternsText = document.getElementById('regex-patterns').value;
        
        if (!patternsText) {
            throw new Error('请输入正则表达式模式');
        }

        let patterns;
        try {
            patterns = JSON.parse(patternsText);
        } catch (e) {
            throw new Error('正则表达式格式错误');
        }

        const [result] = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            function: extractWithRegexPatterns,
            args: [patterns]
        });
        return result.result;
    }

    // LLM提取
    async extractWithLLM(tab) {
        const provider = document.getElementById('llm-provider').value;
        const apiKey = document.getElementById('llm-api-key').value;
        const instruction = document.getElementById('llm-instruction').value;

        if (!apiKey) {
            throw new Error('请输入API密钥');
        }

        if (!instruction) {
            throw new Error('请输入提取指令');
        }

        // 首先获取页面内容
        const [contentResult] = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            function: extractMarkdownContent,
            args: [this.config]
        });

        const content = contentResult.result;

        // 调用LLM API
        const result = await this.callLLMAPI(provider, apiKey, instruction, content);
        return result;
    }

    // 提取选中内容
    async extractSelection() {
        const tab = await this.getCurrentTab();
        
        const [result] = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            function: () => {
                const selection = window.getSelection();
                if (selection.rangeCount === 0) {
                    return { error: '请先选择要提取的内容' };
                }
                
                const range = selection.getRangeAt(0);
                const container = document.createElement('div');
                container.appendChild(range.cloneContents());
                
                return {
                    html: container.innerHTML,
                    text: container.textContent || container.innerText
                };
            }
        });

        if (result.result.error) {
            this.showError(result.result.error);
            return;
        }

        this.showResult(result.result);
    }

    // 显示加载状态
    showLoading(show) {
        const loading = document.getElementById('loading');
        const results = document.getElementById('results');
        
        if (show) {
            loading.style.display = 'block';
            results.style.display = 'none';
        } else {
            loading.style.display = 'none';
        }
    }

    // 显示结果
    showResult(result) {
        const resultsDiv = document.getElementById('results');
        const contentDiv = document.getElementById('result-content');
        
        contentDiv.innerHTML = '';
        
        if (typeof result === 'string') {
            contentDiv.textContent = result;
        } else {
            contentDiv.textContent = JSON.stringify(result, null, 2);
        }
        
        resultsDiv.style.display = 'block';
    }

    // 显示错误
    showError(message) {
        const resultsDiv = document.getElementById('results');
        const contentDiv = document.getElementById('result-content');
        
        contentDiv.innerHTML = `<div style="color: red; font-weight: bold;">错误: ${message}</div>`;
        resultsDiv.style.display = 'block';
    }

    // 保存配置
    async saveConfig() {
        const config = {
            autoClean: document.getElementById('auto-clean').checked,
            removeAds: document.getElementById('remove-ads').checked,
            extractLinks: document.getElementById('extract-links').checked,
            extractImages: document.getElementById('extract-images').checked,
            minWords: parseInt(document.getElementById('min-words').value),
            excludeTags: document.getElementById('exclude-tags').value,
            apiEndpoint: document.getElementById('api-endpoint').value,
            firecrawlKey: document.getElementById('firecrawl-key').value
        };

        await chrome.storage.sync.set(config);
        this.config = config;
        
        // 显示保存成功提示
        const btn = document.getElementById('save-config');
        const originalText = btn.textContent;
        btn.textContent = '✅ 已保存';
        setTimeout(() => {
            btn.textContent = originalText;
        }, 2000);
    }

    // 复制结果
    async copyResult() {
        const content = document.getElementById('result-content').textContent;
        await navigator.clipboard.writeText(content);
        
        const btn = document.getElementById('copy-result');
        const originalText = btn.textContent;
        btn.textContent = '✅ 已复制';
        setTimeout(() => {
            btn.textContent = originalText;
        }, 2000);
    }

    // 下载结果
    downloadResult() {
        const content = document.getElementById('result-content').textContent;
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `crawl4ai-result-${Date.now()}.txt`;
        a.click();
        
        URL.revokeObjectURL(url);
    }

    // 保存到历史
    async saveToHistory(url, mode, result) {
        const history = await this.getHistory();
        const item = {
            url,
            mode,
            result: typeof result === 'string' ? result.substring(0, 200) + '...' : JSON.stringify(result).substring(0, 200) + '...',
            timestamp: Date.now()
        };
        
        history.unshift(item);
        
        // 只保留最近50条记录
        if (history.length > 50) {
            history.splice(50);
        }
        
        await chrome.storage.local.set({ history });
        this.loadHistory();
    }

    // 获取历史记录
    async getHistory() {
        const result = await chrome.storage.local.get(['history']);
        return result.history || [];
    }

    // 加载历史记录
    async loadHistory() {
        const history = await this.getHistory();
        const historyList = document.getElementById('history-list');
        
        if (history.length === 0) {
            historyList.innerHTML = '<p style="text-align: center; color: #6c757d;">暂无历史记录</p>';
            return;
        }
        
        historyList.innerHTML = history.map(item => `
            <div class="history-item">
                <div class="url">${this.truncateUrl(item.url)}</div>
                <div class="time">${new Date(item.timestamp).toLocaleString()} - ${item.mode}</div>
            </div>
        `).join('');
    }

    // 清空历史
    async clearHistory() {
        await chrome.storage.local.remove(['history']);
        this.loadHistory();
    }

    // 截断URL显示
    truncateUrl(url) {
        if (url.length > 40) {
            return url.substring(0, 40) + '...';
        }
        return url;
    }

    // 调用LLM API
    async callLLMAPI(provider, apiKey, instruction, content) {
        // 这里实现不同LLM提供商的API调用
        // 由于浏览器扩展的限制，这里只是示例
        throw new Error('LLM功能需要后端服务支持，请使用Docker版本');
    }

    // 注入到页面的函数需要独立定义
}

// 注入到页面的独立函数
function extractMarkdownContent(config) {
    // 移除不需要的元素
    const excludeTags = config.excludeTags.split(',').map(tag => tag.trim());
    excludeTags.forEach(tag => {
        document.querySelectorAll(tag).forEach(el => el.remove());
    });

    // 移除广告相关元素
    if (config.removeAds) {
        const adSelectors = [
            '[class*="ad"]', '[id*="ad"]', '[class*="advertisement"]',
            '[class*="banner"]', '[class*="popup"]', '.sponsored'
        ];
        adSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(el => el.remove());
        });
    }

    // 获取主要内容
    const contentSelectors = [
        'main', 'article', '.content', '.post', '.entry',
        '#content', '#main', '.main-content'
    ];

    let mainContent = null;
    for (const selector of contentSelectors) {
        const element = document.querySelector(selector);
        if (element) {
            mainContent = element;
            break;
        }
    }

    if (!mainContent) {
        mainContent = document.body;
    }

    // 转换为Markdown格式
    return htmlToMarkdown(mainContent.innerHTML);
}



// HTML转Markdown的简单实现
function htmlToMarkdown(html) {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // 处理标题
    for (let i = 1; i <= 6; i++) {
        tempDiv.querySelectorAll(`h${i}`).forEach(h => {
            h.textContent = '#'.repeat(i) + ' ' + h.textContent;
        });
    }

    // 处理链接
    tempDiv.querySelectorAll('a').forEach(a => {
        if (a.href) {
            a.textContent = `[${a.textContent}](${a.href})`;
        }
    });

    // 处理图片
    tempDiv.querySelectorAll('img').forEach(img => {
        if (img.src) {
            img.outerHTML = `![${img.alt || ''}](${img.src})`;
        }
    });

    // 处理列表
    tempDiv.querySelectorAll('li').forEach(li => {
        li.textContent = '- ' + li.textContent;
    });

    return tempDiv.textContent || tempDiv.innerText;
}

// CSS选择器提取函数
function extractWithCSSSelector(selector, fields, config) {
    const elements = document.querySelectorAll(selector);
    const results = [];

    elements.forEach((element, index) => {
        const item = { index };

        if (fields.length === 0) {
            // 如果没有指定字段，返回元素的文本内容
            item.content = element.textContent || element.innerText;
        } else {
            // 根据字段配置提取数据
            fields.forEach(field => {
                const fieldElements = element.querySelectorAll(field.selector);
                if (fieldElements.length > 0) {
                    const fieldElement = fieldElements[0];

                    switch (field.type) {
                        case 'text':
                            item[field.name] = fieldElement.textContent || fieldElement.innerText;
                            break;
                        case 'attribute':
                            item[field.name] = fieldElement.getAttribute(field.attribute);
                            break;
                        case 'html':
                            item[field.name] = fieldElement.innerHTML;
                            break;
                        default:
                            item[field.name] = fieldElement.textContent || fieldElement.innerText;
                    }
                }
            });
        }

        results.push(item);
    });

    return results;
}

// 正则表达式提取函数
function extractWithRegexPatterns(patterns) {
    const content = document.body.textContent || document.body.innerText;
    const results = {};

    Object.keys(patterns).forEach(key => {
        const pattern = new RegExp(patterns[key], 'g');
        const matches = content.match(pattern) || [];
        results[key] = matches;
    });

    return results;
}

// 初始化弹窗
document.addEventListener('DOMContentLoaded', () => {
    new Crawl4AIPopup();
});
