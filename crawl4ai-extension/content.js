// 内容脚本 - 在网页中运行的脚本
class Crawl4AIContent {
    constructor() {
        this.isInjected = false;
        this.selectedElement = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.injectUI();
        this.setupKeyboardShortcuts();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 监听来自popup和background的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true;
        });

        // 监听鼠标事件用于元素选择
        document.addEventListener('mouseover', (e) => {
            if (this.isSelectionMode) {
                this.highlightElement(e.target);
            }
        });

        document.addEventListener('click', (e) => {
            if (this.isSelectionMode) {
                e.preventDefault();
                e.stopPropagation();
                this.selectElement(e.target);
            }
        });

        // 监听键盘事件
        document.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
    }

    // 处理消息
    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'extract-page':
                    const pageContent = await this.extractPageContent();
                    sendResponse({ success: true, data: pageContent });
                    break;

                case 'extract-selection':
                    const selectionContent = this.extractSelection();
                    sendResponse({ success: true, data: selectionContent });
                    break;

                case 'start-element-selection':
                    this.startElementSelection();
                    sendResponse({ success: true });
                    break;

                case 'stop-element-selection':
                    this.stopElementSelection();
                    sendResponse({ success: true });
                    break;

                case 'extract-element':
                    const elementContent = this.extractElement(request.selector);
                    sendResponse({ success: true, data: elementContent });
                    break;

                case 'get-page-info':
                    const pageInfo = this.getPageInfo();
                    sendResponse({ success: true, data: pageInfo });
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Content script error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 注入UI元素
    injectUI() {
        if (this.isInjected) return;

        // 创建浮动按钮
        this.createFloatingButton();
        
        // 创建选择模式提示
        this.createSelectionOverlay();

        this.isInjected = true;
    }

    // 创建浮动按钮
    createFloatingButton() {
        const button = document.createElement('div');
        button.id = 'crawl4ai-floating-btn';
        button.innerHTML = '🚀';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            opacity: 0.8;
        `;

        button.addEventListener('mouseenter', () => {
            button.style.opacity = '1';
            button.style.transform = 'scale(1.1)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.opacity = '0.8';
            button.style.transform = 'scale(1)';
        });

        button.addEventListener('click', () => {
            this.showQuickMenu();
        });

        document.body.appendChild(button);
        this.floatingButton = button;
    }

    // 创建选择模式覆盖层
    createSelectionOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'crawl4ai-selection-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(102, 126, 234, 0.1);
            z-index: 9999;
            display: none;
            pointer-events: none;
        `;

        const hint = document.createElement('div');
        hint.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
            font-size: 16px;
            text-align: center;
            pointer-events: none;
        `;
        hint.innerHTML = '点击要提取的元素<br><small>按 ESC 键退出选择模式</small>';

        overlay.appendChild(hint);
        document.body.appendChild(overlay);
        this.selectionOverlay = overlay;
    }

    // 显示快速菜单
    showQuickMenu() {
        const menu = document.createElement('div');
        menu.id = 'crawl4ai-quick-menu';
        menu.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.2);
            z-index: 10001;
            min-width: 200px;
            overflow: hidden;
        `;

        const menuItems = [
            { text: '📄 提取整个页面', action: 'extract-page' },
            { text: '📝 提取选中内容', action: 'extract-selection' },
            { text: '🎯 选择元素提取', action: 'select-element' },
            { text: '⚙️ 打开设置', action: 'open-settings' }
        ];

        menuItems.forEach(item => {
            const menuItem = document.createElement('div');
            menuItem.style.cssText = `
                padding: 12px 16px;
                cursor: pointer;
                border-bottom: 1px solid #eee;
                transition: background 0.2s ease;
            `;
            menuItem.textContent = item.text;

            menuItem.addEventListener('mouseenter', () => {
                menuItem.style.background = '#f5f5f5';
            });

            menuItem.addEventListener('mouseleave', () => {
                menuItem.style.background = 'white';
            });

            menuItem.addEventListener('click', () => {
                this.handleQuickMenuAction(item.action);
                this.hideQuickMenu();
            });

            menu.appendChild(menuItem);
        });

        // 点击外部关闭菜单
        const closeMenu = (e) => {
            if (!menu.contains(e.target) && e.target !== this.floatingButton) {
                this.hideQuickMenu();
                document.removeEventListener('click', closeMenu);
            }
        };

        setTimeout(() => {
            document.addEventListener('click', closeMenu);
        }, 100);

        document.body.appendChild(menu);
        this.quickMenu = menu;
    }

    // 隐藏快速菜单
    hideQuickMenu() {
        if (this.quickMenu) {
            this.quickMenu.remove();
            this.quickMenu = null;
        }
    }

    // 处理快速菜单操作
    async handleQuickMenuAction(action) {
        switch (action) {
            case 'extract-page':
                const pageContent = await this.extractPageContent();
                this.showResult('页面内容', pageContent);
                break;

            case 'extract-selection':
                const selectionContent = this.extractSelection();
                if (selectionContent) {
                    this.showResult('选中内容', selectionContent);
                } else {
                    this.showNotification('请先选择要提取的内容', 'warning');
                }
                break;

            case 'select-element':
                this.startElementSelection();
                break;

            case 'open-settings':
                chrome.runtime.sendMessage({ action: 'open-options' });
                break;
        }
    }

    // 设置键盘快捷键
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+Shift+E: 快速提取
            if (e.ctrlKey && e.shiftKey && e.key === 'E') {
                e.preventDefault();
                this.showQuickMenu();
            }

            // ESC: 退出选择模式
            if (e.key === 'Escape' && this.isSelectionMode) {
                this.stopElementSelection();
            }
        });
    }

    // 开始元素选择模式
    startElementSelection() {
        this.isSelectionMode = true;
        this.selectionOverlay.style.display = 'block';
        document.body.style.cursor = 'crosshair';
        
        this.showNotification('元素选择模式已启动', 'info');
    }

    // 停止元素选择模式
    stopElementSelection() {
        this.isSelectionMode = false;
        this.selectionOverlay.style.display = 'none';
        document.body.style.cursor = 'default';
        
        // 移除高亮
        this.removeHighlight();
    }

    // 高亮元素
    highlightElement(element) {
        this.removeHighlight();
        
        element.style.outline = '2px solid #667eea';
        element.style.outlineOffset = '2px';
        this.highlightedElement = element;
    }

    // 移除高亮
    removeHighlight() {
        if (this.highlightedElement) {
            this.highlightedElement.style.outline = '';
            this.highlightedElement.style.outlineOffset = '';
            this.highlightedElement = null;
        }
    }

    // 选择元素
    selectElement(element) {
        this.selectedElement = element;
        this.stopElementSelection();
        
        const content = this.extractElementContent(element);
        this.showResult('元素内容', content);
    }

    // 提取页面内容
    async extractPageContent() {
        const config = await this.getConfig();
        
        // 移除不需要的元素
        const excludeTags = config.excludeTags.split(',').map(tag => tag.trim());
        const tempDoc = document.cloneNode(true);
        
        excludeTags.forEach(tag => {
            tempDoc.querySelectorAll(tag).forEach(el => el.remove());
        });

        // 获取主要内容
        const contentSelectors = [
            'main', 'article', '.content', '.post', '.entry',
            '#content', '#main', '.main-content'
        ];

        let mainContent = null;
        for (const selector of contentSelectors) {
            const element = tempDoc.querySelector(selector);
            if (element) {
                mainContent = element;
                break;
            }
        }

        if (!mainContent) {
            mainContent = tempDoc.body;
        }

        return mainContent.textContent || mainContent.innerText;
    }

    // 提取选中内容
    extractSelection() {
        const selection = window.getSelection();
        if (selection.rangeCount === 0) {
            return null;
        }
        
        return selection.toString();
    }

    // 提取元素内容
    extractElementContent(element) {
        return {
            text: element.textContent || element.innerText,
            html: element.innerHTML,
            tagName: element.tagName,
            className: element.className,
            id: element.id
        };
    }

    // 获取页面信息
    getPageInfo() {
        return {
            title: document.title,
            url: window.location.href,
            domain: window.location.hostname,
            wordCount: (document.body.textContent || document.body.innerText).split(/\s+/).length,
            linkCount: document.querySelectorAll('a').length,
            imageCount: document.querySelectorAll('img').length
        };
    }

    // 获取配置
    async getConfig() {
        return new Promise((resolve) => {
            chrome.storage.sync.get([
                'autoClean', 'removeAds', 'extractLinks', 'extractImages',
                'minWords', 'excludeTags'
            ], (result) => {
                resolve({
                    autoClean: result.autoClean || true,
                    removeAds: result.removeAds || true,
                    extractLinks: result.extractLinks || false,
                    extractImages: result.extractImages || false,
                    minWords: result.minWords || 10,
                    excludeTags: result.excludeTags || 'nav,footer,aside'
                });
            });
        });
    }

    // 显示结果
    showResult(title, content) {
        // 创建结果弹窗
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10002;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background: white;
            border-radius: 8px;
            max-width: 80%;
            max-height: 80%;
            overflow: auto;
            padding: 20px;
        `;

        modalContent.innerHTML = `
            <h3>${title}</h3>
            <pre style="white-space: pre-wrap; font-family: monospace; background: #f5f5f5; padding: 15px; border-radius: 4px;">${typeof content === 'string' ? content : JSON.stringify(content, null, 2)}</pre>
            <div style="margin-top: 15px; text-align: right;">
                <button id="copy-btn" style="margin-right: 10px; padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">复制</button>
                <button id="close-btn" style="padding: 8px 16px; background: #ccc; color: black; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
            </div>
        `;

        modal.appendChild(modalContent);
        document.body.appendChild(modal);

        // 事件处理
        modalContent.querySelector('#copy-btn').addEventListener('click', () => {
            navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content, null, 2));
            this.showNotification('内容已复制到剪贴板', 'success');
        });

        modalContent.querySelector('#close-btn').addEventListener('click', () => {
            modal.remove();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'error' ? '#ff4757' : type === 'success' ? '#2ed573' : type === 'warning' ? '#ffa502' : '#667eea'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 10003;
            font-family: Arial, sans-serif;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // 处理键盘事件
    handleKeydown(e) {
        // 可以在这里添加更多键盘快捷键
    }
}

// 初始化内容脚本
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new Crawl4AIContent();
    });
} else {
    new Crawl4AIContent();
}
