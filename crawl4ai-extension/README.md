# 🚀 Crawl4AI Browser Extension

一个功能强大的浏览器扩展，将Crawl4AI的智能网页内容提取功能直接集成到您的浏览器中。

## ✨ 主要功能

### 🎯 智能内容提取
- **Markdown格式**: 自动转换网页内容为结构化Markdown
- **CSS选择器**: 使用CSS选择器精确提取特定内容
- **正则表达式**: 基于模式匹配提取数据（邮箱、电话、URL等）
- **LLM智能提取**: 集成多种LLM进行智能内容理解和提取

### 🔧 多种提取方式
- **整页提取**: 一键提取整个网页的主要内容
- **选中内容提取**: 提取用户选中的特定文本
- **元素选择提取**: 可视化选择页面元素进行提取
- **右键菜单**: 便捷的右键菜单快速访问

### 🎨 用户友好界面
- **浮动按钮**: 页面右上角的快速访问按钮
- **弹窗界面**: 功能完整的弹窗操作界面
- **选项页面**: 详细的设置和配置页面
- **实时预览**: 提取结果的实时预览和编辑

### 🤖 LLM集成支持
- **OpenAI**: 支持GPT-4等模型
- **Claude**: 支持Anthropic Claude系列
- **Gemini**: 支持Google Gemini
- **本地LLM**: 支持Ollama等本地部署的模型

### ⚡ 高性能特性
- **智能缓存**: 避免重复提取，提高效率
- **并发处理**: 支持多个页面同时处理
- **内容过滤**: 自动移除广告和无关内容
- **格式优化**: 智能清理和格式化提取结果

## 📦 安装方法

### 方法1: 开发者模式安装（推荐）

1. **下载扩展文件**
   ```bash
   git clone <repository-url>
   cd crawl4ai-extension
   ```

2. **打开Chrome扩展管理页面**
   - 在Chrome中访问 `chrome://extensions/`
   - 或者点击菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 在页面右上角打开"开发者模式"开关

4. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `crawl4ai-extension` 文件夹

5. **完成安装**
   - 扩展将出现在浏览器工具栏中
   - 点击扩展图标开始使用

### 方法2: 打包安装

1. **打包扩展**
   - 在扩展管理页面点击"打包扩展程序"
   - 选择 `crawl4ai-extension` 文件夹
   - 生成 `.crx` 文件

2. **安装打包文件**
   - 将 `.crx` 文件拖拽到扩展管理页面
   - 确认安装

## 🚀 快速开始

### 基础使用

1. **访问任意网页**
2. **点击扩展图标** 或 **使用快捷键 Ctrl+Shift+E**
3. **选择提取模式**:
   - Markdown: 获取结构化文本
   - CSS选择器: 精确提取特定元素
   - 正则表达式: 模式匹配提取
   - LLM智能提取: AI驱动的内容理解

4. **点击"开始提取"**
5. **查看和使用结果**

### 高级功能

#### CSS选择器提取示例
```json
{
  "selector": ".article-content",
  "fields": [
    {"name": "title", "selector": "h1", "type": "text"},
    {"name": "author", "selector": ".author", "type": "text"},
    {"name": "content", "selector": ".content", "type": "text"}
  ]
}
```

#### 正则表达式提取示例
```json
{
  "emails": "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b",
  "phones": "\\b\\d{3}-\\d{3}-\\d{4}\\b",
  "urls": "https?://[^\\s<>\"{}|\\\\^`[\\]]+"
}
```

#### LLM提取示例
- **提取指令**: "请提取这篇文章的标题、作者、发布时间和主要内容"
- **结构化输出**: 自动生成JSON格式的结构化数据

## ⚙️ 配置说明

### 通用设置
- **自动清理内容**: 移除不必要的空白和格式
- **移除广告内容**: 自动识别并移除广告元素
- **提取链接**: 在结果中包含页面链接
- **提取图片**: 在结果中包含图片信息

### 提取设置
- **最小字数阈值**: 忽略少于指定字数的内容块
- **排除标签**: 指定要排除的HTML标签
- **默认提取模式**: 设置默认的提取方式
- **智能内容检测**: 自动识别主要内容区域

### API设置
- **Crawl4AI服务器**: 配置Docker服务器地址
- **LLM API密钥**: 配置各种LLM服务的API密钥
- **连接超时**: 设置API请求超时时间

### 高级设置
- **并发请求数**: 同时处理的最大请求数
- **缓存设置**: 配置本地缓存大小和过期时间
- **调试模式**: 启用详细日志输出

## 🔑 快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl + Shift + E` | 打开快速提取菜单 |
| `ESC` | 退出元素选择模式 |

## 🔗 与Crawl4AI服务器集成

扩展可以与Crawl4AI Docker服务器配合使用，获得完整功能：

1. **启动Crawl4AI服务器**
   ```bash
   docker run -d -p 11235:11235 --name crawl4ai unclecode/crawl4ai:0.6.0-r1
   ```

2. **配置服务器地址**
   - 在扩展设置中将API端点设置为 `http://localhost:11235`

3. **享受完整功能**
   - 高级内容处理
   - 深度爬取
   - 更多提取策略

## 📊 功能对比

| 功能 | 扩展版本 | 完整版本 |
|------|----------|----------|
| 基础内容提取 | ✅ | ✅ |
| CSS选择器提取 | ✅ | ✅ |
| 正则表达式提取 | ✅ | ✅ |
| Markdown生成 | ✅ | ✅ |
| LLM集成 | ⚠️ 需要API | ✅ |
| 深度爬取 | ❌ | ✅ |
| 浏览器自动化 | ❌ | ✅ |
| 大规模并发 | ❌ | ✅ |

## 🛠️ 开发说明

### 项目结构
```
crawl4ai-extension/
├── manifest.json          # 扩展清单文件
├── popup.html             # 弹窗界面
├── options.html           # 选项页面
├── background.js          # 后台脚本
├── content.js             # 内容脚本
├── scripts/
│   ├── popup.js          # 弹窗逻辑
│   └── options.js        # 选项页面逻辑
├── styles/
│   ├── popup.css         # 弹窗样式
│   └── options.css       # 选项页面样式
├── icons/                # 图标文件
└── README.md            # 说明文档
```

### 核心组件

1. **Popup界面** (`popup.html/js`): 主要的用户交互界面
2. **Background脚本** (`background.js`): 处理后台逻辑和API调用
3. **Content脚本** (`content.js`): 在网页中运行，处理内容提取
4. **Options页面** (`options.html/js`): 详细的设置和配置

### 扩展开发

如果您想修改或扩展功能：

1. **修改代码**
2. **在扩展管理页面点击"重新加载"**
3. **测试新功能**

## 🐛 故障排除

### 常见问题

1. **扩展无法加载**
   - 检查是否启用了开发者模式
   - 确认文件夹结构正确
   - 查看控制台错误信息

2. **提取功能不工作**
   - 检查页面是否完全加载
   - 确认CSS选择器是否正确
   - 查看扩展的错误日志

3. **LLM功能无法使用**
   - 确认API密钥配置正确
   - 检查网络连接
   - 验证API服务状态

4. **与Crawl4AI服务器连接失败**
   - 确认Docker容器正在运行
   - 检查端口是否正确映射
   - 测试服务器连接

### 调试方法

1. **启用调试模式**: 在设置中开启调试模式
2. **查看控制台**: 按F12打开开发者工具
3. **检查扩展日志**: 在扩展管理页面查看错误信息

## 📄 许可证

本项目基于 Apache 2.0 许可证开源。

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请：
1. 查看本README文档
2. 在GitHub上提交Issue
3. 访问Crawl4AI官方文档

---

**享受智能网页内容提取的便利！** 🚀
