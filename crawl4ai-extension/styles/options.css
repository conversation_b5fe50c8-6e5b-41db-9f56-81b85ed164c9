/* 选项页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 40px rgba(0,0,0,0.1);
}

/* 头部样式 */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    text-align: center;
}

header h1 {
    font-size: 32px;
    margin-bottom: 10px;
}

header p {
    opacity: 0.9;
    font-size: 16px;
}

/* 导航标签 */
.nav-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    overflow-x: auto;
}

.nav-tab {
    flex: 1;
    padding: 16px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    color: #6c757d;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 120px;
}

.nav-tab.active {
    background: white;
    color: #667eea;
    border-bottom: 3px solid #667eea;
}

.nav-tab:hover:not(.active) {
    background: #e9ecef;
    color: #495057;
}

/* 内容区域 */
.content {
    padding: 40px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-content h2 {
    color: #495057;
    margin-bottom: 30px;
    font-size: 24px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

/* 设置组 */
.setting-group {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    border-left: 4px solid #667eea;
}

.setting-group h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 18px;
}

/* 表单元素 */
label {
    display: block;
    margin-bottom: 20px;
    font-weight: 500;
    color: #495057;
}

label small {
    display: block;
    font-weight: normal;
    color: #6c757d;
    margin-top: 5px;
    font-size: 12px;
}

input[type="text"],
input[type="url"],
input[type="password"],
input[type="number"],
select,
textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ced4da;
    border-radius: 8px;
    font-size: 14px;
    margin-top: 8px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 复选框样式 */
.checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    position: relative;
    padding-left: 35px;
    margin-bottom: 15px;
}

.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    width: 0;
    height: 0;
}

.checkmark {
    position: absolute;
    top: 2px;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border: 2px solid #ced4da;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.checkbox-label:hover input ~ .checkmark {
    border-color: #667eea;
}

.checkbox-label input:checked ~ .checkmark {
    background-color: #667eea;
    border-color: #667eea;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-label input:checked ~ .checkmark:after {
    display: block;
}

/* 单选框样式 */
.radio-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.radio-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding-left: 30px;
    position: relative;
}

.radio-label input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    width: 0;
    height: 0;
}

.radio-mark {
    position: absolute;
    top: 2px;
    left: 0;
    height: 18px;
    width: 18px;
    background-color: #fff;
    border: 2px solid #ced4da;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.radio-label:hover input ~ .radio-mark {
    border-color: #667eea;
}

.radio-label input:checked ~ .radio-mark {
    background-color: #667eea;
    border-color: #667eea;
}

.radio-mark:after {
    content: "";
    position: absolute;
    display: none;
    top: 3px;
    left: 3px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
}

.radio-label input:checked ~ .radio-mark:after {
    display: block;
}

/* 按钮样式 */
.primary-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 140px;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.secondary-btn {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #ced4da;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    margin-right: 10px;
    margin-bottom: 10px;
}

.secondary-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.danger-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.danger-btn:hover {
    background: #c82333;
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

/* 快捷键显示 */
.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.shortcut-item:last-child {
    border-bottom: none;
}

kbd {
    background: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 4px 8px;
    font-family: monospace;
    font-size: 12px;
    color: #495057;
}

/* 状态指示器 */
.status-indicator {
    margin-top: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 13px;
    display: none;
}

.status-indicator.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.status-indicator.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

/* 关于页面 */
.about-section {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.logo {
    font-size: 64px;
    margin-bottom: 20px;
}

.about-section h3 {
    font-size: 28px;
    color: #495057;
    margin-bottom: 10px;
}

.version {
    color: #6c757d;
    font-size: 16px;
    margin-bottom: 20px;
}

.description {
    font-size: 16px;
    line-height: 1.6;
    color: #495057;
    margin-bottom: 30px;
}

.links {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.link-btn {
    display: inline-block;
    padding: 10px 20px;
    background: #667eea;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 14px;
}

.link-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.features {
    text-align: left;
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.features h4 {
    color: #495057;
    margin-bottom: 15px;
    text-align: center;
}

.features ul {
    list-style: none;
    padding: 0;
}

.features li {
    padding: 8px 0;
    color: #495057;
}

.credits {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    font-size: 14px;
    color: #6c757d;
    line-height: 1.6;
}

/* 底部 */
footer {
    background: #f8f9fa;
    padding: 30px 40px;
    border-top: 1px solid #e9ecef;
}

.save-section {
    text-align: center;
}

.save-status {
    margin-top: 15px;
    font-size: 14px;
    font-weight: 500;
}

.save-status.success {
    color: #28a745;
}

.save-status.error {
    color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 0;
    }
    
    header {
        padding: 30px 20px;
    }
    
    .content {
        padding: 20px;
    }
    
    .nav-tabs {
        flex-direction: column;
    }
    
    .nav-tab {
        flex: none;
    }
    
    .radio-group {
        gap: 15px;
    }
    
    .links {
        flex-direction: column;
        align-items: center;
    }
    
    .button-group {
        justify-content: center;
    }
}
