/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 600px;
}

.container {
    width: 400px;
    min-height: 600px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* 头部样式 */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

header p {
    opacity: 0.9;
    font-size: 14px;
}

/* 标签页样式 */
.tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 12px;
    color: #6c757d;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: white;
    color: #667eea;
    border-bottom: 2px solid #667eea;
}

.tab-btn:hover {
    background: #e9ecef;
}

/* 内容区域 */
.tab-content {
    display: none;
    padding: 20px;
    max-height: 480px;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

.section {
    margin-bottom: 20px;
}

.section h3 {
    color: #495057;
    margin-bottom: 12px;
    font-size: 16px;
}

/* 表单元素 */
label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
    font-size: 13px;
}

input, select, textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 13px;
    margin-bottom: 12px;
    transition: border-color 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

textarea {
    resize: vertical;
    min-height: 60px;
}

/* 复选框样式 */
input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
    margin-bottom: 0;
}

label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

/* 按钮样式 */
.primary-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    width: 100%;
    margin-bottom: 8px;
    transition: transform 0.2s ease;
}

.primary-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.secondary-btn {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #ced4da;
    padding: 10px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    width: 100%;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.secondary-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

/* 配置区域 */
.config-section {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 30px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 结果区域 */
.results {
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 15px;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
}

.result-header h3 {
    margin: 0;
    color: #495057;
}

.result-actions {
    display: flex;
    gap: 8px;
}

.result-actions button {
    padding: 6px 12px;
    font-size: 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    background: #667eea;
    color: white;
    transition: background 0.2s ease;
}

.result-actions button:hover {
    background: #5a6fd8;
}

.result-content {
    padding: 15px;
    max-height: 200px;
    overflow-y: auto;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 12px;
    line-height: 1.4;
    background: white;
    border-radius: 0 0 8px 8px;
}

/* 历史记录 */
.history-list {
    max-height: 300px;
    overflow-y: auto;
}

.history-item {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    border-left: 4px solid #667eea;
}

.history-item .url {
    font-weight: 500;
    color: #495057;
    margin-bottom: 4px;
    font-size: 13px;
}

.history-item .time {
    font-size: 11px;
    color: #6c757d;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
