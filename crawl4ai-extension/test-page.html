<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crawl4AI 扩展测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            font-size: 18px;
        }
        
        .article-content {
            margin: 30px 0;
        }
        
        .article-meta {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .author {
            font-weight: bold;
            color: #667eea;
        }
        
        .date {
            color: #666;
            font-size: 14px;
        }
        
        .content {
            font-size: 16px;
            line-height: 1.8;
            color: #333;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        
        .product-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .product {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        
        .product-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .product-price {
            color: #e74c3c;
            font-size: 18px;
            font-weight: bold;
        }
        
        .product-description {
            color: #666;
            font-size: 14px;
            margin: 10px 0;
        }
        
        .contact-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
        }
        
        .links {
            margin: 20px 0;
        }
        
        .links a {
            color: #667eea;
            text-decoration: none;
            margin-right: 15px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
        }
        
        /* 广告样式 - 用于测试广告过滤 */
        .ad, .advertisement, .banner {
            background: #ff6b6b;
            color: white;
            padding: 10px;
            text-align: center;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .popup {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #ff9f43;
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">🚀</div>
            <h1>Crawl4AI 扩展测试页面</h1>
            <p class="subtitle">测试各种内容提取功能</p>
        </header>

        <main class="article-content">
            <article>
                <div class="article-meta">
                    <div class="author">作者: 张三</div>
                    <div class="date">发布时间: 2024-01-15</div>
                    <div class="category">分类: 技术教程</div>
                </div>

                <div class="content">
                    <h2>什么是Crawl4AI？</h2>
                    <p>
                        Crawl4AI是一个专为AI应用优化的开源网络爬虫和内容提取工具。它提供了智能的内容提取功能，
                        可以将网页内容转换为结构化的数据格式，特别适合用于训练大语言模型和构建RAG系统。
                    </p>

                    <div class="highlight">
                        <strong>重要提示:</strong> 这个页面包含了各种类型的内容，用于测试Crawl4AI扩展的不同提取功能。
                    </div>

                    <h3>主要特性</h3>
                    <ul>
                        <li>🎯 智能内容识别和提取</li>
                        <li>🔧 多种提取策略支持</li>
                        <li>🤖 LLM集成和AI驱动分析</li>
                        <li>⚡ 高性能异步处理</li>
                        <li>🌐 支持多种网页格式</li>
                    </ul>

                    <h3>使用场景</h3>
                    <p>
                        Crawl4AI广泛应用于数据科学、AI研究、内容分析等领域。无论是学术研究还是商业应用，
                        都能从其强大的内容提取能力中受益。
                    </p>
                </div>
            </article>

            <!-- 广告内容 - 用于测试广告过滤 -->
            <div class="ad">
                这是一个广告横幅 - 应该被过滤掉
            </div>

            <section class="product-list">
                <h2>相关产品</h2>
                
                <div class="product">
                    <div class="product-title">Crawl4AI Pro</div>
                    <div class="product-price">¥299/月</div>
                    <div class="product-description">
                        专业版本，包含高级AI分析功能和企业级支持。
                    </div>
                </div>

                <div class="product">
                    <div class="product-title">Crawl4AI Enterprise</div>
                    <div class="product-price">¥999/月</div>
                    <div class="product-description">
                        企业版本，支持大规模部署和定制化开发。
                    </div>
                </div>

                <div class="product">
                    <div class="product-title">Crawl4AI API</div>
                    <div class="product-price">¥0.01/请求</div>
                    <div class="product-description">
                        按需付费的API服务，灵活便捷。
                    </div>
                </div>
            </section>

            <div class="advertisement">
                另一个广告区域 - 也应该被过滤
            </div>

            <section class="contact-info">
                <h3>联系信息</h3>
                <p><strong>邮箱:</strong> <EMAIL></p>
                <p><strong>电话:</strong> +86-138-0013-8000</p>
                <p><strong>地址:</strong> 北京市朝阳区科技园区创新大厦</p>
                <p><strong>网站:</strong> https://crawl4ai.com</p>
                <p><strong>GitHub:</strong> https://github.com/unclecode/crawl4ai</p>
            </section>

            <div class="links">
                <h3>相关链接</h3>
                <a href="https://docs.crawl4ai.com">官方文档</a>
                <a href="https://github.com/unclecode/crawl4ai">GitHub仓库</a>
                <a href="https://crawl4ai.com/blog">技术博客</a>
                <a href="https://crawl4ai.com/examples">使用示例</a>
                <a href="mailto:<EMAIL>">技术支持</a>
            </div>

            <div class="banner">
                横幅广告 - 测试广告过滤功能
            </div>
        </main>

        <footer>
            <p>&copy; 2024 Crawl4AI. 保留所有权利。</p>
            <p>本页面仅用于测试Crawl4AI浏览器扩展功能。</p>
        </footer>
    </div>

    <!-- 弹窗广告 - 用于测试 -->
    <div class="popup">
        弹窗广告 - 应该被移除
    </div>

    <script>
        // 添加一些动态内容用于测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面已加载，可以开始测试Crawl4AI扩展功能');
            
            // 模拟动态加载的内容
            setTimeout(() => {
                const dynamicContent = document.createElement('div');
                dynamicContent.innerHTML = '<p><strong>动态加载的内容:</strong> 这段内容是通过JavaScript动态添加的。</p>';
                dynamicContent.style.background = '#e8f5e8';
                dynamicContent.style.padding = '15px';
                dynamicContent.style.borderRadius = '5px';
                dynamicContent.style.margin = '20px 0';
                
                const mainContent = document.querySelector('.article-content');
                mainContent.appendChild(dynamicContent);
            }, 2000);
        });
    </script>
</body>
</html>
