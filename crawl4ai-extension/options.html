<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crawl4AI 设置</title>
    <link rel="stylesheet" href="styles/options.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 Crawl4AI 设置</h1>
            <p>配置您的网页内容提取偏好</p>
        </header>

        <nav class="nav-tabs">
            <button class="nav-tab active" data-tab="general">通用设置</button>
            <button class="nav-tab" data-tab="extraction">提取设置</button>
            <button class="nav-tab" data-tab="api">API设置</button>
            <button class="nav-tab" data-tab="advanced">高级设置</button>
            <button class="nav-tab" data-tab="about">关于</button>
        </nav>

        <main class="content">
            <!-- 通用设置 -->
            <section id="general-tab" class="tab-content active">
                <h2>通用设置</h2>
                
                <div class="setting-group">
                    <h3>内容处理</h3>
                    <label class="checkbox-label">
                        <input type="checkbox" id="auto-clean">
                        <span class="checkmark"></span>
                        自动清理内容
                        <small>移除不必要的空白和格式</small>
                    </label>
                    
                    <label class="checkbox-label">
                        <input type="checkbox" id="remove-ads">
                        <span class="checkmark"></span>
                        移除广告内容
                        <small>自动识别并移除广告元素</small>
                    </label>
                    
                    <label class="checkbox-label">
                        <input type="checkbox" id="extract-links">
                        <span class="checkmark"></span>
                        提取链接
                        <small>在结果中包含页面链接</small>
                    </label>
                    
                    <label class="checkbox-label">
                        <input type="checkbox" id="extract-images">
                        <span class="checkmark"></span>
                        提取图片
                        <small>在结果中包含图片信息</small>
                    </label>
                </div>

                <div class="setting-group">
                    <h3>快捷键</h3>
                    <div class="shortcut-item">
                        <span>快速提取菜单</span>
                        <kbd>Ctrl + Shift + E</kbd>
                    </div>
                    <div class="shortcut-item">
                        <span>退出选择模式</span>
                        <kbd>ESC</kbd>
                    </div>
                </div>

                <div class="setting-group">
                    <h3>界面设置</h3>
                    <label class="checkbox-label">
                        <input type="checkbox" id="show-floating-button">
                        <span class="checkmark"></span>
                        显示浮动按钮
                        <small>在页面右上角显示快速访问按钮</small>
                    </label>
                    
                    <label class="checkbox-label">
                        <input type="checkbox" id="show-notifications">
                        <span class="checkmark"></span>
                        显示通知
                        <small>操作完成后显示通知消息</small>
                    </label>
                </div>
            </section>

            <!-- 提取设置 -->
            <section id="extraction-tab" class="tab-content">
                <h2>提取设置</h2>
                
                <div class="setting-group">
                    <h3>内容过滤</h3>
                    <label>
                        最小字数阈值
                        <input type="number" id="min-words" min="1" max="1000">
                        <small>忽略少于此字数的内容块</small>
                    </label>
                    
                    <label>
                        排除标签
                        <input type="text" id="exclude-tags" placeholder="nav,footer,aside">
                        <small>用逗号分隔要排除的HTML标签</small>
                    </label>
                    
                    <label>
                        排除CSS选择器
                        <input type="text" id="exclude-selectors" placeholder=".ad,.popup,.banner">
                        <small>用逗号分隔要排除的CSS选择器</small>
                    </label>
                </div>

                <div class="setting-group">
                    <h3>默认提取模式</h3>
                    <div class="radio-group">
                        <label class="radio-label">
                            <input type="radio" name="default-mode" value="markdown">
                            <span class="radio-mark"></span>
                            Markdown格式
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="default-mode" value="text">
                            <span class="radio-mark"></span>
                            纯文本
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="default-mode" value="html">
                            <span class="radio-mark"></span>
                            HTML格式
                        </label>
                    </div>
                </div>

                <div class="setting-group">
                    <h3>智能提取</h3>
                    <label class="checkbox-label">
                        <input type="checkbox" id="smart-content-detection">
                        <span class="checkmark"></span>
                        智能内容检测
                        <small>自动识别主要内容区域</small>
                    </label>
                    
                    <label class="checkbox-label">
                        <input type="checkbox" id="preserve-formatting">
                        <span class="checkmark"></span>
                        保留格式
                        <small>尽可能保留原始格式</small>
                    </label>
                </div>
            </section>

            <!-- API设置 -->
            <section id="api-tab" class="tab-content">
                <h2>API设置</h2>
                
                <div class="setting-group">
                    <h3>Crawl4AI服务器</h3>
                    <label>
                        服务器地址
                        <input type="url" id="api-endpoint" placeholder="http://localhost:11235">
                        <small>Crawl4AI Docker服务器的地址</small>
                    </label>
                    
                    <label>
                        连接超时 (秒)
                        <input type="number" id="api-timeout" min="5" max="300" value="30">
                    </label>
                    
                    <button id="test-connection" class="secondary-btn">测试连接</button>
                    <div id="connection-status" class="status-indicator"></div>
                </div>

                <div class="setting-group">
                    <h3>LLM API设置</h3>
                    <label>
                        OpenAI API密钥
                        <input type="password" id="openai-key" placeholder="sk-proj-...">
                        <small>支持GPT-4o, GPT-4, GPT-3.5-turbo等模型</small>
                    </label>

                    <label>
                        Claude API密钥
                        <input type="password" id="claude-key" placeholder="sk-ant-...">
                        <small>支持Claude-3.5-sonnet, Claude-3-opus等模型</small>
                    </label>

                    <label>
                        Gemini API密钥
                        <input type="password" id="gemini-key" placeholder="AI...">
                        <small>支持Gemini-1.5-pro, Gemini-1.5-flash等模型</small>
                    </label>

                    <label>
                        DeepSeek API密钥
                        <input type="password" id="deepseek-key" placeholder="sk-...">
                        <small>支持deepseek-chat, deepseek-coder等模型</small>
                    </label>

                    <label>
                        本地LLM地址
                        <input type="url" id="local-llm-endpoint" placeholder="http://localhost:11434">
                        <small>Ollama或其他本地LLM服务地址</small>
                    </label>

                    <div class="button-group">
                        <button id="test-openai" class="secondary-btn">测试OpenAI</button>
                        <button id="test-claude" class="secondary-btn">测试Claude</button>
                        <button id="test-gemini" class="secondary-btn">测试Gemini</button>
                        <button id="test-local" class="secondary-btn">测试本地LLM</button>
                    </div>
                </div>

                <div class="setting-group">
                    <h3>第三方服务</h3>
                    <label>
                        Firecrawl API密钥
                        <input type="password" id="firecrawl-key" placeholder="fc-...">
                        <small>Firecrawl服务的API密钥</small>
                    </label>
                </div>
            </section>

            <!-- 高级设置 -->
            <section id="advanced-tab" class="tab-content">
                <h2>高级设置</h2>
                
                <div class="setting-group">
                    <h3>性能设置</h3>
                    <label>
                        并发请求数
                        <input type="number" id="concurrent-requests" min="1" max="10" value="3">
                        <small>同时处理的最大请求数</small>
                    </label>
                    
                    <label>
                        缓存大小 (MB)
                        <input type="number" id="cache-size" min="10" max="1000" value="100">
                        <small>本地缓存的最大大小</small>
                    </label>
                    
                    <label>
                        缓存过期时间 (小时)
                        <input type="number" id="cache-ttl" min="1" max="168" value="24">
                    </label>
                </div>

                <div class="setting-group">
                    <h3>调试设置</h3>
                    <label class="checkbox-label">
                        <input type="checkbox" id="debug-mode">
                        <span class="checkmark"></span>
                        调试模式
                        <small>在控制台输出详细日志</small>
                    </label>
                    
                    <label class="checkbox-label">
                        <input type="checkbox" id="save-raw-html">
                        <span class="checkmark"></span>
                        保存原始HTML
                        <small>在提取结果中包含原始HTML</small>
                    </label>
                </div>

                <div class="setting-group">
                    <h3>数据管理</h3>
                    <div class="button-group">
                        <button id="export-settings" class="secondary-btn">导出设置</button>
                        <button id="import-settings" class="secondary-btn">导入设置</button>
                        <button id="reset-settings" class="danger-btn">重置设置</button>
                    </div>
                    
                    <div class="button-group">
                        <button id="clear-cache" class="secondary-btn">清空缓存</button>
                        <button id="clear-history" class="secondary-btn">清空历史</button>
                    </div>
                </div>
            </section>

            <!-- 关于 -->
            <section id="about-tab" class="tab-content">
                <h2>关于 Crawl4AI</h2>
                
                <div class="about-section">
                    <div class="logo">🚀</div>
                    <h3>Crawl4AI Browser Extension</h3>
                    <p class="version">版本 1.0.0</p>
                    
                    <p class="description">
                        Crawl4AI是一个专为AI应用优化的开源网页爬虫和内容提取工具。
                        这个浏览器扩展让您可以直接在浏览器中使用Crawl4AI的强大功能。
                    </p>
                    
                    <div class="links">
                        <a href="https://github.com/unclecode/crawl4ai" target="_blank" class="link-btn">
                            📚 GitHub仓库
                        </a>
                        <a href="https://docs.crawl4ai.com" target="_blank" class="link-btn">
                            📖 文档
                        </a>
                        <a href="https://github.com/unclecode/crawl4ai/issues" target="_blank" class="link-btn">
                            🐛 报告问题
                        </a>
                    </div>
                    
                    <div class="features">
                        <h4>主要功能</h4>
                        <ul>
                            <li>🎯 智能内容提取</li>
                            <li>🔧 多种提取策略</li>
                            <li>🤖 LLM集成支持</li>
                            <li>⚡ 高性能处理</li>
                            <li>🎨 用户友好界面</li>
                            <li>🔒 隐私保护</li>
                        </ul>
                    </div>
                    
                    <div class="credits">
                        <p>
                            <strong>开发者:</strong> UncleCode<br>
                            <strong>许可证:</strong> Apache 2.0<br>
                            <strong>基于:</strong> Crawl4AI v0.6.0
                        </p>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <div class="save-section">
                <button id="save-all" class="primary-btn">💾 保存所有设置</button>
                <div id="save-status" class="save-status"></div>
            </div>
        </footer>
    </div>

    <script src="scripts/options.js"></script>
</body>
</html>
