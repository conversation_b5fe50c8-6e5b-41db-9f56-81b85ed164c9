# 🚀 Crawl4AI 浏览器扩展使用示例

## 📋 目录
1. [基础使用示例](#基础使用示例)
2. [LLM智能提取示例](#llm智能提取示例)
3. [CSS选择器提取示例](#css选择器提取示例)
4. [正则表达式提取示例](#正则表达式提取示例)
5. [实际应用场景](#实际应用场景)

## 🎯 基础使用示例

### 示例1: 提取新闻文章内容

**场景**: 从新闻网站提取文章的主要内容

**步骤**:
1. 访问新闻网站 (如: https://www.bbc.com/news)
2. 点击扩展图标
3. 选择"Markdown"模式
4. 点击"开始提取"

**预期结果**:
```markdown
# 新闻标题

发布时间: 2024-01-15
作者: 记者姓名

## 主要内容

新闻的主要内容会被自动提取并格式化为Markdown格式...

## 相关链接
- [相关新闻1](https://example.com/news1)
- [相关新闻2](https://example.com/news2)
```

### 示例2: 清理网页内容

**场景**: 移除广告和无关内容，获取纯净的文章内容

**配置**:
- ✅ 自动清理内容
- ✅ 移除广告内容
- 最小字数: 20
- 排除标签: nav,footer,aside,.ad,.banner

**结果**: 获得干净、无广告的文章内容

## 🤖 LLM智能提取示例

### 示例1: 使用OpenAI提取产品信息

**配置**:
```json
{
  "提取模式": "LLM智能提取",
  "LLM提供商": "OpenAI (GPT-4o)",
  "API密钥": "sk-proj-your-key-here",
  "提取指令": "请提取这个页面上所有产品的信息，包括名称、价格、描述和评分，以JSON格式返回"
}
```

**输入网页**: 电商产品页面

**输出结果**:
```json
{
  "products": [
    {
      "name": "iPhone 15 Pro",
      "price": "¥8999",
      "description": "搭载A17 Pro芯片的专业级智能手机",
      "rating": "4.8/5",
      "features": ["钛金属设计", "48MP主摄", "USB-C接口"]
    },
    {
      "name": "MacBook Air M3",
      "price": "¥9499", 
      "description": "轻薄便携的专业笔记本电脑",
      "rating": "4.9/5",
      "features": ["M3芯片", "18小时续航", "13.6英寸显示屏"]
    }
  ]
}
```

### 示例2: 使用Claude分析文章观点

**配置**:
```json
{
  "LLM提供商": "Claude (3.5-sonnet)",
  "提取指令": "分析这篇文章的主要观点、论据和结论，并评估其可信度"
}
```

**输出结果**:
```json
{
  "analysis": {
    "main_viewpoint": "人工智能将显著改变未来工作方式",
    "arguments": [
      "自动化将替代重复性工作",
      "AI将创造新的工作机会",
      "需要重新培训劳动力"
    ],
    "conclusion": "适应AI发展是必要的",
    "credibility": "高 - 基于多项研究和专家意见",
    "sources": ["MIT研究", "世界经济论坛报告"]
  }
}
```

### 示例3: 使用本地Ollama模型

**配置**:
```json
{
  "LLM提供商": "本地LLM (Ollama)",
  "本地LLM地址": "http://localhost:11434",
  "模型": "llama3.1",
  "提取指令": "总结这个网页的关键信息，用中文回答"
}
```

**优势**: 
- 🔒 数据隐私保护
- 💰 无API费用
- ⚡ 本地处理速度快

## 🎯 CSS选择器提取示例

### 示例1: 提取电商产品列表

**目标网页**: 电商网站产品列表页

**CSS配置**:
```json
{
  "CSS选择器": ".product-item",
  "提取字段": [
    {
      "name": "title",
      "selector": ".product-title",
      "type": "text"
    },
    {
      "name": "price", 
      "selector": ".price",
      "type": "text"
    },
    {
      "name": "image",
      "selector": "img",
      "type": "attribute",
      "attribute": "src"
    },
    {
      "name": "link",
      "selector": "a",
      "type": "attribute", 
      "attribute": "href"
    }
  ]
}
```

**输出结果**:
```json
[
  {
    "index": 0,
    "title": "无线蓝牙耳机",
    "price": "¥299",
    "image": "https://example.com/images/headphone.jpg",
    "link": "https://example.com/product/123"
  },
  {
    "index": 1,
    "title": "智能手表",
    "price": "¥1299",
    "image": "https://example.com/images/watch.jpg", 
    "link": "https://example.com/product/456"
  }
]
```

### 示例2: 提取新闻列表

**CSS配置**:
```json
{
  "CSS选择器": "article.news-item",
  "提取字段": [
    {
      "name": "headline",
      "selector": "h2.headline",
      "type": "text"
    },
    {
      "name": "summary",
      "selector": ".summary",
      "type": "text"
    },
    {
      "name": "author",
      "selector": ".author",
      "type": "text"
    },
    {
      "name": "publishTime",
      "selector": ".publish-time",
      "type": "text"
    }
  ]
}
```

## 🔍 正则表达式提取示例

### 示例1: 提取联系信息

**正则配置**:
```json
{
  "emails": "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b",
  "phones": "\\b(?:\\+86[\\s-]?)?1[3-9]\\d{9}\\b",
  "websites": "https?://[^\\s<>\"{}|\\\\^`[\\]]+",
  "addresses": "\\b\\d+号[^\\n]*"
}
```

**输出结果**:
```json
{
  "emails": [
    "<EMAIL>",
    "<EMAIL>"
  ],
  "phones": [
    "+86 138-0013-8000",
    "************"
  ],
  "websites": [
    "https://www.example.com",
    "https://blog.company.com"
  ],
  "addresses": [
    "北京市朝阳区建国路88号",
    "上海市浦东新区世纪大道123号"
  ]
}
```

### 示例2: 提取价格和数字信息

**正则配置**:
```json
{
  "prices": "[¥$€£]\\s?[\\d,]+(?:\\.\\d{2})?",
  "percentages": "\\d+(?:\\.\\d+)?%",
  "dates": "\\d{4}[-/年]\\d{1,2}[-/月]\\d{1,2}[日]?",
  "numbers": "\\b\\d{1,3}(?:,\\d{3})*(?:\\.\\d+)?\\b"
}
```

## 🎨 实际应用场景

### 场景1: 学术研究数据收集

**目标**: 从学术网站收集论文信息

**方法**: LLM智能提取
**指令**: "提取这篇论文的标题、作者、摘要、关键词和发表信息"

**应用价值**:
- 📚 快速整理文献资料
- 🔍 建立研究数据库
- 📊 分析研究趋势

### 场景2: 竞品分析

**目标**: 分析竞争对手的产品信息

**方法**: CSS选择器 + LLM分析
**步骤**:
1. 用CSS选择器提取产品基础信息
2. 用LLM分析产品特点和优势

**应用价值**:
- 📈 市场调研
- 💡 产品策略制定
- 🎯 定价参考

### 场景3: 内容创作素材收集

**目标**: 为写作收集相关资料

**方法**: Markdown提取 + LLM总结
**流程**:
1. 提取文章主要内容
2. LLM生成内容摘要
3. 整理为写作素材

**应用价值**:
- ✍️ 提高写作效率
- 📖 丰富内容来源
- 🎨 激发创作灵感

### 场景4: 新闻监控和分析

**目标**: 监控特定主题的新闻动态

**方法**: 定期访问新闻网站 + LLM分析
**配置**:
```json
{
  "提取指令": "分析这条新闻的关键信息：主题、影响、相关人物、时间地点，并评估其重要性"
}
```

**应用价值**:
- 📰 及时获取行业动态
- 📊 分析市场趋势
- ⚠️ 风险预警

## 💡 使用技巧和最佳实践

### 🎯 提高提取准确性

1. **明确指令**: 使用具体、详细的提取指令
2. **示例引导**: 在指令中提供期望输出的示例
3. **分步处理**: 复杂任务分解为多个简单步骤
4. **格式要求**: 明确指定输出格式 (JSON、表格等)

### ⚡ 优化性能

1. **合理缓存**: 利用历史记录避免重复提取
2. **选择合适模型**: 根据任务复杂度选择LLM模型
3. **控制内容长度**: 过长内容可能影响处理效果
4. **批量处理**: 相似页面使用相同配置

### 🔧 故障排除

1. **API限制**: 注意各服务商的调用频率限制
2. **网络问题**: 确保网络连接稳定
3. **内容变化**: 网站结构变化时及时调整选择器
4. **结果验证**: 定期检查提取结果的准确性

## 🚀 进阶用法

### 组合使用多种方法

1. **CSS + LLM**: 先用CSS提取结构化数据，再用LLM分析
2. **正则 + LLM**: 用正则提取特定模式，用LLM理解语义
3. **分层提取**: 不同层级的信息使用不同方法

### 自定义工作流

1. **保存常用配置**: 将常用的提取配置保存为模板
2. **建立提取标准**: 为不同类型网站制定提取规范
3. **结果后处理**: 对提取结果进行进一步清理和格式化

通过这些示例和技巧，您可以充分发挥Crawl4AI浏览器扩展的强大功能，高效地完成各种网页内容提取任务！
