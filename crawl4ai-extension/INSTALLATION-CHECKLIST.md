# ✅ Crawl4AI 浏览器扩展安装检查清单

## 📋 安装前准备

### 🔧 系统要求检查
- [ ] Chrome 88+ 或 Edge 88+ 浏览器
- [ ] 支持Manifest V3的现代浏览器
- [ ] 开发者模式权限 (用于加载未打包扩展)

### 📁 文件准备检查
- [ ] 下载完整的 `crawl4ai-extension` 文件夹
- [ ] 确认所有必需文件存在:
  - [ ] `manifest.json`
  - [ ] `popup.html` 和 `popup.js`
  - [ ] `options.html` 和 `options.js`
  - [ ] `background.js`
  - [ ] `content.js`
  - [ ] `styles/` 文件夹及CSS文件

### 🎨 图标文件准备 (重要!)
- [ ] 在 `icons/` 文件夹中准备以下文件:
  - [ ] `icon16.png` (16×16像素)
  - [ ] `icon32.png` (32×32像素)
  - [ ] `icon48.png` (48×48像素)
  - [ ] `icon128.png` (128×128像素)

**图标生成方法**:
1. [ ] 使用提供的 `icon-generator.html` 生成图标
2. [ ] 或使用在线工具创建PNG图标
3. [ ] 确保图标背景透明，使用品牌色彩

## 🚀 安装步骤

### 步骤1: 打开扩展管理页面
- [ ] 在Chrome中访问 `chrome://extensions/`
- [ ] 或通过菜单: 更多工具 → 扩展程序

### 步骤2: 启用开发者模式
- [ ] 点击页面右上角的"开发者模式"开关
- [ ] 确认开关已开启 (显示为蓝色)

### 步骤3: 加载扩展
- [ ] 点击"加载已解压的扩展程序"按钮
- [ ] 选择 `crawl4ai-extension` 文件夹
- [ ] 点击"选择文件夹"确认

### 步骤4: 验证安装
- [ ] 扩展出现在扩展列表中
- [ ] 扩展状态显示为"已启用"
- [ ] 浏览器工具栏显示扩展图标
- [ ] 图标显示正常 (不是默认的拼图图标)

## ⚙️ 基础配置

### 🎯 初始设置
- [ ] 点击扩展图标打开弹窗
- [ ] 切换到"配置"标签页
- [ ] 配置基础选项:
  - [ ] ✅ 自动清理内容
  - [ ] ✅ 移除广告内容
  - [ ] 最小字数: 10
  - [ ] 排除标签: nav,footer,aside

### 🔧 高级设置 (可选)
- [ ] 右键扩展图标 → 选项
- [ ] 配置详细设置:
  - [ ] 提取模式偏好
  - [ ] 性能参数
  - [ ] 缓存设置

## 🤖 LLM API配置 (可选)

### OpenAI配置
- [ ] 获取OpenAI API密钥 (https://platform.openai.com)
- [ ] 在选项页面 → API设置 → 输入OpenAI API密钥
- [ ] 点击"测试OpenAI"验证连接
- [ ] 确认连接状态显示"成功"

### Claude配置
- [ ] 获取Claude API密钥 (https://console.anthropic.com)
- [ ] 输入Claude API密钥
- [ ] 测试连接确认可用

### Gemini配置
- [ ] 获取Gemini API密钥 (https://makersuite.google.com)
- [ ] 输入Gemini API密钥
- [ ] 测试连接确认可用

### 本地LLM配置 (Ollama)
- [ ] 安装Ollama (https://ollama.ai)
- [ ] 启动Ollama服务: `ollama serve`
- [ ] 下载模型: `ollama pull llama3.1`
- [ ] 在扩展中配置地址: `http://localhost:11434`
- [ ] 测试本地连接

## 🧪 功能测试

### 基础功能测试
- [ ] 打开测试页面 `test-page.html`
- [ ] 测试Markdown提取:
  - [ ] 点击扩展图标
  - [ ] 选择"Markdown"模式
  - [ ] 点击"开始提取"
  - [ ] 确认提取结果正确显示

### CSS选择器测试
- [ ] 选择"CSS选择器"模式
- [ ] 输入选择器: `.product`
- [ ] 点击"开始提取"
- [ ] 确认提取到产品信息

### 正则表达式测试
- [ ] 选择"正则表达式"模式
- [ ] 输入模式: `{"emails": "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b"}`
- [ ] 确认提取到邮箱地址

### LLM功能测试 (如已配置)
- [ ] 选择"LLM智能提取"模式
- [ ] 选择LLM提供商
- [ ] 输入提取指令
- [ ] 确认AI分析结果

### 界面功能测试
- [ ] 测试浮动按钮 (页面右上角🚀)
- [ ] 测试右键菜单功能
- [ ] 测试快捷键 `Ctrl+Shift+E`
- [ ] 测试选项页面各项设置

## 🔧 故障排除

### 常见问题检查

#### 扩展无法加载
- [ ] 检查文件夹结构是否完整
- [ ] 确认manifest.json语法正确
- [ ] 查看扩展管理页面的错误信息
- [ ] 确认图标文件存在

#### 图标不显示
- [ ] 确认icons文件夹包含所有尺寸图标
- [ ] 检查图标文件名是否正确
- [ ] 重新加载扩展
- [ ] 清除浏览器缓存

#### 功能不工作
- [ ] 按F12查看控制台错误
- [ ] 确认页面完全加载
- [ ] 检查网络连接
- [ ] 重启浏览器

#### LLM功能问题
- [ ] 验证API密钥正确性
- [ ] 检查账户余额/配额
- [ ] 确认网络可访问API服务
- [ ] 尝试简化提取指令

### 调试方法
- [ ] 启用调试模式 (选项页面 → 高级设置)
- [ ] 查看扩展错误日志
- [ ] 检查网络请求状态
- [ ] 测试不同网页和配置

## 📊 性能优化

### 基础优化
- [ ] 设置合理的缓存大小 (100MB)
- [ ] 配置适当的缓存过期时间 (24小时)
- [ ] 限制并发请求数 (3个)
- [ ] 定期清理历史记录

### 高级优化
- [ ] 根据使用频率调整API配置
- [ ] 优化CSS选择器的复杂度
- [ ] 合理使用LLM功能避免过度消费
- [ ] 监控扩展内存使用情况

## 🎯 使用建议

### 新手用户
- [ ] 从Markdown模式开始使用
- [ ] 熟悉基础配置选项
- [ ] 练习使用测试页面
- [ ] 逐步尝试高级功能

### 进阶用户
- [ ] 学习CSS选择器语法
- [ ] 掌握正则表达式模式
- [ ] 配置LLM API服务
- [ ] 自定义提取工作流

### 开发者用户
- [ ] 启用调试模式
- [ ] 研究扩展源代码
- [ ] 贡献功能改进
- [ ] 分享使用经验

## ✅ 安装完成确认

完成以下所有检查项后，您的Crawl4AI浏览器扩展就可以正常使用了：

- [ ] 扩展成功加载并显示在工具栏
- [ ] 基础提取功能正常工作
- [ ] 配置选项可以正常保存
- [ ] 图标和界面显示正常
- [ ] (可选) LLM API配置成功
- [ ] 所有测试用例通过

## 🎉 开始使用

恭喜！您已成功安装并配置了Crawl4AI浏览器扩展。

**下一步**:
1. 📖 阅读 `USAGE-EXAMPLES.md` 了解详细使用方法
2. 🚀 开始您的智能网页内容提取之旅
3. 💡 探索更多高级功能和应用场景

**获取帮助**:
- 📚 查看项目文档
- 🐛 报告问题到GitHub Issues
- 💬 参与社区讨论

享受高效的网页内容提取体验！🚀
