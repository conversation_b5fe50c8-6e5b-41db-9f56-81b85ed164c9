<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crawl4AI</title>
    <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 Crawl4AI</h1>
            <p>智能网页内容提取</p>
        </header>

        <div class="tabs">
            <button class="tab-btn active" data-tab="extract">内容提取</button>
            <button class="tab-btn" data-tab="config">配置</button>
            <button class="tab-btn" data-tab="history">历史</button>
        </div>

        <!-- 内容提取标签页 -->
        <div id="extract-tab" class="tab-content active">
            <div class="section">
                <label>提取模式:</label>
                <select id="extraction-mode">
                    <option value="markdown">Markdown</option>
                    <option value="css">CSS选择器</option>
                    <option value="regex">正则表达式</option>
                    <option value="llm">LLM智能提取</option>
                </select>
            </div>

            <!-- CSS选择器配置 -->
            <div id="css-config" class="config-section" style="display: none;">
                <label>CSS选择器:</label>
                <input type="text" id="css-selector" placeholder="例如: .article-content">
                <label>提取字段:</label>
                <textarea id="css-fields" placeholder='[{"name": "title", "selector": "h1", "type": "text"}]'></textarea>
            </div>

            <!-- 正则表达式配置 -->
            <div id="regex-config" class="config-section" style="display: none;">
                <label>正则模式:</label>
                <textarea id="regex-patterns" placeholder='{"emails": "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b"}'></textarea>
            </div>

            <!-- LLM配置 -->
            <div id="llm-config" class="config-section" style="display: none;">
                <label>LLM提供商:</label>
                <select id="llm-provider">
                    <option value="openai">OpenAI (GPT-4o)</option>
                    <option value="claude">Claude (3.5-sonnet)</option>
                    <option value="gemini">Gemini (1.5-pro)</option>
                    <option value="deepseek">DeepSeek</option>
                    <option value="local">本地LLM (Ollama)</option>
                </select>
                <label>API密钥:</label>
                <input type="password" id="llm-api-key" placeholder="输入API密钥">
                <label>提取指令:</label>
                <textarea id="llm-instruction" placeholder="请提取页面中的主要信息，以JSON格式返回结果..."></textarea>
                <label>
                    <input type="checkbox" id="llm-structured-output"> 要求结构化输出 (JSON格式)
                </label>
            </div>

            <div class="actions">
                <button id="extract-btn" class="primary-btn">🚀 开始提取</button>
                <button id="extract-selection-btn" class="secondary-btn">📝 提取选中内容</button>
            </div>

            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>正在提取内容...</p>
            </div>

            <div id="results" class="results" style="display: none;">
                <div class="result-header">
                    <h3>提取结果</h3>
                    <div class="result-actions">
                        <button id="copy-result">📋 复制</button>
                        <button id="download-result">💾 下载</button>
                        <button id="save-result">💾 保存</button>
                    </div>
                </div>
                <div id="result-content" class="result-content"></div>
            </div>
        </div>

        <!-- 配置标签页 -->
        <div id="config-tab" class="tab-content">
            <div class="section">
                <h3>通用设置</h3>
                <label>
                    <input type="checkbox" id="auto-clean"> 自动清理内容
                </label>
                <label>
                    <input type="checkbox" id="remove-ads"> 移除广告内容
                </label>
                <label>
                    <input type="checkbox" id="extract-links"> 提取链接
                </label>
                <label>
                    <input type="checkbox" id="extract-images"> 提取图片
                </label>
            </div>

            <div class="section">
                <h3>过滤设置</h3>
                <label>最小字数:</label>
                <input type="number" id="min-words" value="10" min="1">
                <label>排除标签:</label>
                <input type="text" id="exclude-tags" placeholder="nav,footer,aside" value="nav,footer,aside">
            </div>

            <div class="section">
                <h3>API设置</h3>
                <label>Crawl4AI服务器:</label>
                <input type="url" id="api-endpoint" placeholder="http://localhost:11235" value="http://localhost:11235">
                <label>Firecrawl API密钥:</label>
                <input type="password" id="firecrawl-key" placeholder="fc-...">
            </div>

            <button id="save-config" class="primary-btn">💾 保存配置</button>
        </div>

        <!-- 历史标签页 -->
        <div id="history-tab" class="tab-content">
            <div class="section">
                <h3>提取历史</h3>
                <div id="history-list" class="history-list">
                    <!-- 历史记录将在这里显示 -->
                </div>
                <button id="clear-history" class="secondary-btn">🗑️ 清空历史</button>
            </div>
        </div>
    </div>

    <script src="scripts/popup.js"></script>
</body>
</html>
