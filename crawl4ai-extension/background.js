// 后台脚本 - 处理扩展的后台逻辑
class Crawl4AIBackground {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupContextMenus();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 扩展安装时的处理
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                this.onInstall();
            } else if (details.reason === 'update') {
                this.onUpdate(details.previousVersion);
            }
        });

        // 处理来自popup和content script的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 标签页更新时的处理
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.onTabUpdated(tabId, tab);
            }
        });
    }

    // 设置右键菜单
    setupContextMenus() {
        chrome.contextMenus.create({
            id: 'crawl4ai-extract-page',
            title: '🚀 提取整个页面',
            contexts: ['page']
        });

        chrome.contextMenus.create({
            id: 'crawl4ai-extract-selection',
            title: '📝 提取选中内容',
            contexts: ['selection']
        });

        chrome.contextMenus.create({
            id: 'crawl4ai-extract-link',
            title: '🔗 提取链接内容',
            contexts: ['link']
        });

        // 右键菜单点击处理
        chrome.contextMenus.onClicked.addListener((info, tab) => {
            this.handleContextMenuClick(info, tab);
        });
    }

    // 扩展安装处理
    async onInstall() {
        // 设置默认配置
        const defaultConfig = {
            autoClean: true,
            removeAds: true,
            extractLinks: false,
            extractImages: false,
            minWords: 10,
            excludeTags: 'nav,footer,aside',
            apiEndpoint: 'http://localhost:11235',
            firecrawlKey: ''
        };

        await chrome.storage.sync.set(defaultConfig);

        // 打开欢迎页面
        chrome.tabs.create({
            url: chrome.runtime.getURL('options.html')
        });
    }

    // 扩展更新处理
    async onUpdate(previousVersion) {
        console.log(`Crawl4AI updated from ${previousVersion} to ${chrome.runtime.getManifest().version}`);
        
        // 可以在这里处理版本迁移逻辑
        this.migrateSettings(previousVersion);
    }

    // 设置迁移
    async migrateSettings(previousVersion) {
        // 根据版本进行设置迁移
        const config = await chrome.storage.sync.get();
        
        // 示例：添加新的默认设置
        if (!config.hasOwnProperty('extractImages')) {
            await chrome.storage.sync.set({ extractImages: false });
        }
    }

    // 处理消息
    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'extract-content':
                    const result = await this.extractContent(request.data, sender.tab);
                    sendResponse({ success: true, data: result });
                    break;

                case 'call-api':
                    const apiResult = await this.callExternalAPI(request.data);
                    sendResponse({ success: true, data: apiResult });
                    break;

                case 'save-extraction':
                    await this.saveExtraction(request.data);
                    sendResponse({ success: true });
                    break;

                case 'get-tab-info':
                    const tabInfo = await this.getTabInfo(sender.tab.id);
                    sendResponse({ success: true, data: tabInfo });
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Background script error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 右键菜单点击处理
    async handleContextMenuClick(info, tab) {
        try {
            switch (info.menuItemId) {
                case 'crawl4ai-extract-page':
                    await this.extractPageContent(tab);
                    break;

                case 'crawl4ai-extract-selection':
                    await this.extractSelectionContent(tab, info.selectionText);
                    break;

                case 'crawl4ai-extract-link':
                    await this.extractLinkContent(tab, info.linkUrl);
                    break;
            }
        } catch (error) {
            console.error('Context menu error:', error);
            this.showNotification('提取失败', error.message, 'error');
        }
    }

    // 提取页面内容
    async extractPageContent(tab) {
        const config = await chrome.storage.sync.get();
        
        const [result] = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            function: extractMarkdownContent,
            args: [config]
        });

        if (result.result) {
            await this.saveExtraction({
                url: tab.url,
                title: tab.title,
                content: result.result,
                type: 'page',
                timestamp: Date.now()
            });

            this.showNotification('提取成功', '页面内容已提取并保存', 'success');
        }
    }

    // 提取选中内容
    async extractSelectionContent(tab, selectionText) {
        if (!selectionText) {
            this.showNotification('提取失败', '没有选中的内容', 'error');
            return;
        }

        await this.saveExtraction({
            url: tab.url,
            title: tab.title,
            content: selectionText,
            type: 'selection',
            timestamp: Date.now()
        });

        this.showNotification('提取成功', '选中内容已保存', 'success');
    }

    // 提取链接内容
    async extractLinkContent(tab, linkUrl) {
        if (!linkUrl) {
            this.showNotification('提取失败', '无效的链接', 'error');
            return;
        }

        // 在新标签页中打开链接并提取内容
        const newTab = await chrome.tabs.create({ url: linkUrl, active: false });
        
        // 等待页面加载完成
        await this.waitForTabLoad(newTab.id);
        
        // 提取内容
        await this.extractPageContent(newTab);
        
        // 关闭标签页
        await chrome.tabs.remove(newTab.id);
    }

    // 等待标签页加载完成
    waitForTabLoad(tabId) {
        return new Promise((resolve) => {
            const listener = (updatedTabId, changeInfo) => {
                if (updatedTabId === tabId && changeInfo.status === 'complete') {
                    chrome.tabs.onUpdated.removeListener(listener);
                    setTimeout(resolve, 1000); // 额外等待1秒确保内容加载
                }
            };
            chrome.tabs.onUpdated.addListener(listener);
        });
    }

    // 保存提取结果
    async saveExtraction(data) {
        const history = await this.getHistory();
        history.unshift(data);
        
        // 只保留最近100条记录
        if (history.length > 100) {
            history.splice(100);
        }
        
        await chrome.storage.local.set({ history });
    }

    // 获取历史记录
    async getHistory() {
        const result = await chrome.storage.local.get(['history']);
        return result.history || [];
    }

    // 调用外部API
    async callExternalAPI(data) {
        const config = await chrome.storage.sync.get(['apiEndpoint']);
        const endpoint = config.apiEndpoint || 'http://localhost:11235';

        const response = await fetch(`${endpoint}/crawl`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`API调用失败: ${response.status}`);
        }

        return await response.json();
    }

    // 获取标签页信息
    async getTabInfo(tabId) {
        const tab = await chrome.tabs.get(tabId);
        return {
            url: tab.url,
            title: tab.title,
            favIconUrl: tab.favIconUrl
        };
    }

    // 标签页更新处理
    async onTabUpdated(tabId, tab) {
        // 可以在这里添加自动提取逻辑
        // 例如：检测特定网站并自动提取内容
    }

    // 显示通知
    showNotification(title, message, type = 'info') {
        const iconUrl = type === 'error' ? 'icons/error.png' : 'icons/icon48.png';
        
        chrome.notifications.create({
            type: 'basic',
            iconUrl: iconUrl,
            title: title,
            message: message
        });
    }
}

// 注入函数定义（与popup.js中相同）
function extractMarkdownContent(config) {
    // 移除不需要的元素
    const excludeTags = config.excludeTags.split(',').map(tag => tag.trim());
    excludeTags.forEach(tag => {
        document.querySelectorAll(tag).forEach(el => el.remove());
    });

    // 移除广告相关元素
    if (config.removeAds) {
        const adSelectors = [
            '[class*="ad"]', '[id*="ad"]', '[class*="advertisement"]',
            '[class*="banner"]', '[class*="popup"]', '.sponsored'
        ];
        adSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(el => el.remove());
        });
    }

    // 获取主要内容
    const contentSelectors = [
        'main', 'article', '.content', '.post', '.entry',
        '#content', '#main', '.main-content'
    ];

    let mainContent = null;
    for (const selector of contentSelectors) {
        const element = document.querySelector(selector);
        if (element) {
            mainContent = element;
            break;
        }
    }

    if (!mainContent) {
        mainContent = document.body;
    }

    // 简单的HTML到文本转换
    return mainContent.textContent || mainContent.innerText;
}

// 初始化后台脚本
new Crawl4AIBackground();
