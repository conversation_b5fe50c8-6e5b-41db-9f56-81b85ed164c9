<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crawl4AI 图标生成器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .control-group {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
        }
        
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .preview-section {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .icon-preview {
            display: inline-flex;
            gap: 20px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .icon-size {
            text-align: center;
        }
        
        .icon-size h3 {
            margin: 10px 0 5px 0;
            color: #495057;
        }
        
        canvas {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            margin: 5px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><rect width="10" height="10" fill="%23f0f0f0"/><rect x="10" y="10" width="10" height="10" fill="%23f0f0f0"/></svg>');
        }
        
        .buttons {
            text-align: center;
            margin-top: 20px;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: transform 0.2s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
        
        .download-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .download-links {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .download-link {
            background: #2196f3;
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            transition: background 0.2s ease;
        }
        
        .download-link:hover {
            background: #1976d2;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .instructions h3 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .instructions ol {
            color: #856404;
            margin-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Crawl4AI 图标生成器</h1>
        
        <div class="controls">
            <div class="control-group">
                <label for="iconType">图标类型:</label>
                <select id="iconType">
                    <option value="rocket">🚀 火箭</option>
                    <option value="spider">🕷️ 蜘蛛</option>
                    <option value="robot">🤖 机器人</option>
                    <option value="document">📄 文档</option>
                    <option value="text">C4A 文字</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="bgStyle">背景样式:</label>
                <select id="bgStyle">
                    <option value="gradient">渐变圆形</option>
                    <option value="solid">纯色圆形</option>
                    <option value="rounded">圆角矩形</option>
                    <option value="transparent">透明背景</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="primaryColor">主色调:</label>
                <input type="color" id="primaryColor" value="#667eea">
            </div>
            
            <div class="control-group">
                <label for="secondaryColor">辅助色:</label>
                <input type="color" id="secondaryColor" value="#764ba2">
            </div>
        </div>
        
        <div class="preview-section">
            <h2>图标预览</h2>
            <div class="icon-preview">
                <div class="icon-size">
                    <h3>16×16</h3>
                    <canvas id="canvas16" width="16" height="16"></canvas>
                </div>
                <div class="icon-size">
                    <h3>32×32</h3>
                    <canvas id="canvas32" width="32" height="32"></canvas>
                </div>
                <div class="icon-size">
                    <h3>48×48</h3>
                    <canvas id="canvas48" width="48" height="48"></canvas>
                </div>
                <div class="icon-size">
                    <h3>128×128</h3>
                    <canvas id="canvas128" width="128" height="128"></canvas>
                </div>
            </div>
        </div>
        
        <div class="buttons">
            <button onclick="generateIcons()">🎨 生成图标</button>
            <button onclick="downloadAll()">📥 下载所有图标</button>
        </div>
        
        <div class="download-section" id="downloadSection" style="display: none;">
            <h3>下载图标文件</h3>
            <p>点击下面的链接下载对应尺寸的图标文件：</p>
            <div class="download-links">
                <a href="#" id="download16" class="download-link">icon16.png</a>
                <a href="#" id="download32" class="download-link">icon32.png</a>
                <a href="#" id="download48" class="download-link">icon48.png</a>
                <a href="#" id="download128" class="download-link">icon128.png</a>
            </div>
        </div>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>选择图标类型和背景样式</li>
                <li>调整颜色配置</li>
                <li>点击"生成图标"预览效果</li>
                <li>点击"下载所有图标"或单独下载</li>
                <li>将下载的PNG文件放入 <code>crawl4ai-extension/icons/</code> 文件夹</li>
                <li>确保文件名为: icon16.png, icon32.png, icon48.png, icon128.png</li>
            </ol>
        </div>
    </div>

    <script>
        const sizes = [16, 32, 48, 128];
        const canvases = {};
        const contexts = {};

        // 初始化画布
        sizes.forEach(size => {
            canvases[size] = document.getElementById(`canvas${size}`);
            contexts[size] = canvases[size].getContext('2d');
        });

        function generateIcons() {
            const iconType = document.getElementById('iconType').value;
            const bgStyle = document.getElementById('bgStyle').value;
            const primaryColor = document.getElementById('primaryColor').value;
            const secondaryColor = document.getElementById('secondaryColor').value;

            sizes.forEach(size => {
                const canvas = canvases[size];
                const ctx = contexts[size];
                
                // 清除画布
                ctx.clearRect(0, 0, size, size);
                
                // 绘制背景
                drawBackground(ctx, size, bgStyle, primaryColor, secondaryColor);
                
                // 绘制图标
                drawIcon(ctx, size, iconType);
            });

            // 显示下载区域
            document.getElementById('downloadSection').style.display = 'block';
            setupDownloadLinks();
        }

        function drawBackground(ctx, size, style, primary, secondary) {
            const center = size / 2;
            const radius = size * 0.4;

            switch (style) {
                case 'gradient':
                    const gradient = ctx.createLinearGradient(0, 0, size, size);
                    gradient.addColorStop(0, primary);
                    gradient.addColorStop(1, secondary);
                    ctx.fillStyle = gradient;
                    ctx.beginPath();
                    ctx.arc(center, center, radius, 0, 2 * Math.PI);
                    ctx.fill();
                    break;

                case 'solid':
                    ctx.fillStyle = primary;
                    ctx.beginPath();
                    ctx.arc(center, center, radius, 0, 2 * Math.PI);
                    ctx.fill();
                    break;

                case 'rounded':
                    const gradient2 = ctx.createLinearGradient(0, 0, size, size);
                    gradient2.addColorStop(0, primary);
                    gradient2.addColorStop(1, secondary);
                    ctx.fillStyle = gradient2;
                    const cornerRadius = size * 0.15;
                    roundRect(ctx, size * 0.1, size * 0.1, size * 0.8, size * 0.8, cornerRadius);
                    ctx.fill();
                    break;

                case 'transparent':
                    // 不绘制背景
                    break;
            }
        }

        function drawIcon(ctx, size, type) {
            const center = size / 2;
            const fontSize = size * 0.5;

            ctx.fillStyle = 'white';
            ctx.font = `${fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            switch (type) {
                case 'rocket':
                    ctx.fillText('🚀', center, center);
                    break;
                case 'spider':
                    ctx.fillText('🕷️', center, center);
                    break;
                case 'robot':
                    ctx.fillText('🤖', center, center);
                    break;
                case 'document':
                    ctx.fillText('📄', center, center);
                    break;
                case 'text':
                    ctx.font = `bold ${fontSize * 0.6}px Arial`;
                    ctx.fillText('C4A', center, center);
                    break;
            }
        }

        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }

        function setupDownloadLinks() {
            sizes.forEach(size => {
                const canvas = canvases[size];
                const link = document.getElementById(`download${size}`);
                link.href = canvas.toDataURL('image/png');
                link.download = `icon${size}.png`;
            });
        }

        function downloadAll() {
            generateIcons();
            setTimeout(() => {
                sizes.forEach(size => {
                    const link = document.getElementById(`download${size}`);
                    link.click();
                });
            }, 100);
        }

        // 监听控件变化，实时更新预览
        ['iconType', 'bgStyle', 'primaryColor', 'secondaryColor'].forEach(id => {
            document.getElementById(id).addEventListener('change', generateIcons);
        });

        // 初始生成
        generateIcons();
    </script>
</body>
</html>
